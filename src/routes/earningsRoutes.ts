import express from 'express';
import { isAuthenticated } from '../middlewares/auth';
import { 
  getTutorEarnings, 
  getEarningsBreakdown 
} from '../controllers/earningsController';
import { 
  createWithdrawalRequest,
  getTutorWithdrawals,
  getAvailableBalance,
  cancelWithdrawalRequest,
  getAdminPendingWithdrawals,
  approveWithdrawalRequest,
  rejectWithdrawalRequest
} from '../controllers/withdrawalController';

const router = express.Router();

// Apply authentication to all routes
router.use(isAuthenticated());

/**
 * GET /api/earnings
 * Get tutor's earnings summary
 * Auth: Tutor only
 */
router.get('/', getTutorEarnings);

/**
 * GET /api/earnings/breakdown
 * Get detailed earnings breakdown with filters
 * Query params: startDate, endDate, limit
 * Auth: Tutor only
 */
router.get('/breakdown', getEarningsBreakdown);

/**
 * GET /api/earnings/balance
 * Get available balance for withdrawal
 * Auth: Tutor only
 */
router.get('/balance', getAvailableBalance);

/**
 * POST /api/earnings/withdraw
 * Create a withdrawal request
 * Body: { amount, withdrawalMethod, destinationDetails, tutorNote? }
 * Auth: Tutor only
 */
router.post('/withdraw', createWithdrawalRequest);

/**
 * GET /api/earnings/withdrawals
 * Get tutor's withdrawal requests
 * Query params: limit
 * Auth: Tutor only
 */
router.get('/withdrawals', getTutorWithdrawals);

/**
 * DELETE /api/earnings/withdrawals/:requestId
 * Cancel a pending withdrawal request
 * Auth: Tutor only
 */
router.delete('/withdrawals/:requestId', cancelWithdrawalRequest);

// Admin routes for managing withdrawals
/**
 * GET /api/earnings/admin/withdrawals/pending
 * Get all pending withdrawal requests (Admin only)
 * Query params: limit
 * Auth: Admin only
 */
router.get('/admin/withdrawals/pending', getAdminPendingWithdrawals);

/**
 * POST /api/earnings/admin/withdrawals/:requestId/approve
 * Approve a withdrawal request (Admin only)
 * Body: { adminNotes? }
 * Auth: Admin only
 */
router.post('/admin/withdrawals/:requestId/approve', approveWithdrawalRequest);

/**
 * POST /api/earnings/admin/withdrawals/:requestId/reject
 * Reject a withdrawal request (Admin only)
 * Body: { rejectionReason, adminNotes? }
 * Auth: Admin only
 */
router.post('/admin/withdrawals/:requestId/reject', rejectWithdrawalRequest);

export default router;
