import { Router } from 'express';
import {
  createCalendar,
  getTutorCalendars,
  updateCalendar,
  deleteCalendar,
  getMyCalendars
} from '../controllers/calendarController';
import {
  getCalendarView
} from '../controllers/calendarViewController';
import { Calendar } from '../models/calendar';
import { isAuthenticated } from '../middlewares/auth';
import { validateTutorOwnership, validateSubscriptionForViewing } from '../middlewares/subscriptionValidation';
import { ensureUserCalendar } from '../middlewares/ensureCalendar';
import { withRequestBody } from '../middlewares/misc';

const calender_router = Router();

// Calendar management routes (for both students and tutors)
calender_router.post('/',
  withRequestBody(),
  isAuthenticated(),
  createCalendar
);

calender_router.get('/my',
  isAuthenticated(),
  ensureUserCalendar,
  getMyCalendars
);

calender_router.put('/:id',
  withRequestBody(),
  isAuthenticated(),
  updateCalendar
);

calender_router.delete('/:id',
  isAuthenticated(),
  deleteCalendar
);

// Public/student routes for viewing tutor calendars
calender_router.get('/tutor/:tutorId',
  isAuthenticated(),
  validateSubscriptionForViewing,  // This allows viewing but adds subscription info
  getTutorCalendars
);

// ============================================================================
// CALENDAR VIEW ROUTES - Enhanced with Schedule Support
// ============================================================================

/**
 * GET /api/calendars/:calendarId/view
 * Get calendar view with events and optional schedule patterns
 * Query params: viewType, startDate, endDate, timezone, includeAvailable, includeBooked, includeSchedulePattern, showScheduleOnly
 */
calender_router.get('/:calendarId/view',
  isAuthenticated(),
  validateSubscriptionForViewing,
  getCalendarView
);

/**
 * GET /api/calendars/:calendarId/schedule-integration
 * Get calendar's schedule integration status and settings
 */
calender_router.get('/:calendarId/schedule-integration',
  isAuthenticated(),
  validateTutorOwnership,
  async (req: any, res: any) => {
    try {
      const { calendarId } = req.params;

      if (!req.user || req.user.role !== 'tutor') {
        return res.status(403).json({
          success: false,
          message: 'Only tutors can view schedule integration settings'
        });
      }

      const calendar = await Calendar.findById(calendarId)
        .populate('defaultScheduleId', 'name isActive weeklySchedule settings');

      if (!calendar) {
        return res.status(404).json({
          success: false,
          message: 'Calendar not found'
        });
      }

      if (calendar.ownerId.toString() !== (req.user._id as any).toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not own this calendar'
        });
      }

      res.json({
        success: true,
        data: {
          calendarId: calendar._id,
          hasSchedule: calendar.hasSchedule,
          defaultSchedule: calendar.defaultScheduleId,
          scheduleSettings: calendar.scheduleSettings,
          displaySettings: calendar.displaySettings,
          integrationStatus: {
            isConfigured: !!calendar.defaultScheduleId,
            autoGeneration: calendar.scheduleSettings?.autoGenerateFromSchedule,
            canOverride: calendar.scheduleSettings?.allowScheduleOverrides
          }
        }
      });

    } catch (error) {
      console.error('Error getting schedule integration:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get schedule integration settings'
      });
    }
  }
);

/**
 * PUT /api/calendars/:calendarId/schedule-integration
 * Update calendar's schedule integration settings
 * Body: { defaultScheduleId, scheduleSettings, displaySettings }
 */
calender_router.put('/:calendarId/schedule-integration',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  async (req: any, res: any) => {
    try {
      const { calendarId } = req.params;
      const { defaultScheduleId, scheduleSettings, displaySettings } = req.body;

      if (!req.user || req.user.role !== 'tutor') {
        return res.status(403).json({
          success: false,
          message: 'Only tutors can update schedule integration settings'
        });
      }

      const calendar = await Calendar.findById(calendarId);
      if (!calendar) {
        return res.status(404).json({
          success: false,
          message: 'Calendar not found'
        });
      }

      if (calendar.ownerId.toString() !== (req.user._id as any).toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not own this calendar'
        });
      }

      // Update schedule integration settings
      if (defaultScheduleId !== undefined) {
        calendar.defaultScheduleId = defaultScheduleId;
        calendar.hasSchedule = !!defaultScheduleId;
      }

      if (scheduleSettings) {
        calendar.scheduleSettings = {
          ...calendar.scheduleSettings,
          ...scheduleSettings
        };
      }

      if (displaySettings) {
        calendar.displaySettings = {
          ...calendar.displaySettings,
          ...displaySettings
        };
      }

      await calendar.save();

      const updatedCalendar = await Calendar.findById(calendarId)
        .populate('defaultScheduleId', 'name isActive');

      res.json({
        success: true,
        message: 'Schedule integration settings updated successfully',
        data: updatedCalendar
      });

    } catch (error) {
      console.error('Error updating schedule integration:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update schedule integration settings'
      });
    }
  }
);

export default calender_router;
