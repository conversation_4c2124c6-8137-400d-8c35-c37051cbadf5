import { Router } from "express";
import { withRequestBody } from "../middlewares/misc";
import {
  createLearner,
  getAllLearners,
  deleteLearner,
  getStudentDashboardStats,
  getStudentUpcomingLessons,
  getStudentCalendarView,
} from "../controllers/student";
import { getProfileById } from "../hooks/profile";
import Student from "../models/student";
import { isAuthenticated } from "../middlewares/auth";

const studentRouter = Router();

// Dashboard routes for authenticated students
const dashboardRouter = Router({ mergeParams: true });

// Dashboard stats endpoint
dashboardRouter.get('/stats', ...isAuthenticated({ role: 'student' }), getStudentDashboardStats);

// Upcoming lessons endpoint
dashboardRouter.get('/upcoming-lessons', ...isAuthenticated({ role: 'student' }), getStudentUpcomingLessons);

// Calendar view endpoint
dashboardRouter.get('/calendar', ...isAuthenticated({ role: 'student' }), getStudentCalendarView);

// Mount dashboard routes
studentRouter.use('/dashboard', dashboardRouter);

// Existing learner management routes (for tutors/admins)
studentRouter
  .route("/")
  .post(withRequestBody(), createLearner)
  .get(getAllLearners);

studentRouter.route("/:id").get(getProfileById(Student)).delete(deleteLearner);

export default studentRouter;
