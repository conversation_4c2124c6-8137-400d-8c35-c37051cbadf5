import { Request, Response } from "express";
import { createOkResponse } from "../utils/misc";
import { Model } from "mongoose";
import { IStudent } from "../models/student";
import { ITutor } from "../models/tutor";
import { createErrorResponse } from "../middlewares/errorHandler";
import { IS_PROD } from "../config/constants";
import {
  getProfileModelByRole,
  updateProfileMedias,
  USER_PROFILE_TYPE,
} from "../utils/profile";
import Admin, { IAdmin } from "../models/admin";
import { sendMail } from "../utils/mailer";
import {
  approveTutorTemplate,
  guideToFindTutorTemplate,
} from "../utils/email-templates";
import { logError } from "../utils/error";
import { ensureUserHasCalendar } from "../utils/calendarUtils";

export type PROFILE_MODEL = Model<ITutor> | Model<IStudent> | Model<IAdmin>;

export type SINGLE_PROFILE_MODEL = Model<ITutor | IStudent | IAdmin>;

export function getProfileById(
  Profile: PROFILE_MODEL
): (req: Request, res: Response) => Promise<void> {
  return async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const profile = await (Profile as SINGLE_PROFILE_MODEL).findOne({
        _id: id,
      });

      if (!profile) {
        createErrorResponse(res, `Profile not found.`, 404);
        return;
      }

      createOkResponse(res, { data: profile });
    } catch (error: any) {
      createErrorResponse(res, error, 500);
    }
  };
}

const isComplete = (profile: USER_PROFILE_TYPE) => {
  if (
    profile.firstname &&
    profile.lastname &&
    profile.email &&
    profile.aboutMe &&
    profile.role &&
    profile.phoneno &&
    profile.countryOfBirth &&
    profile.countryOfResidence &&
    profile.nativeLanguage &&
    profile.timeAvailable.length &&
    profile.daysAvailable.length &&
    profile.image
  ) {
    if (profile.role === "tutor") {
      if (
        profile.teachingSubjects.length &&
        profile.academics.length &&
        profile.teachingExperience &&
        profile.motivatePotentialStudent &&
        profile.headline &&
        profile.introVideo &&
        profile.basePrice
      ) {
        return true;
      }
    } else if (profile.role === "student") {
      if (profile.learningReasons.length && profile.skillsToImprove.length) {
        return true;
      }
    }
  }
  return false;
};

export const onBoardUser = async (req: Request, res: Response) => {
  try {
    const Profile = getProfileModelByRole(req.body.role);

    const userId = req.body.userId;

    const user = req.user;

    if (!userId) {
      createErrorResponse(res, "body.userId is required");
      return;
    }

    const updates = { onBoardingStatus: "ongoing", ...req.body };

    if (user.onBoardingStatus === "complete") {
      createErrorResponse(res, "Profile onboarding complete", 401);
      return;
    }

    const errorDetails = await updateProfileMedias(updates, req.user);

    if (isComplete({ ...req.user, ...updates, email: req.user.email })) {
      updates.onBoardingStatus = "complete";
    }

    delete updates.firstname;
    delete updates.lastname;
    delete updates.email;
    delete updates.role;
    delete updates.provider;
    delete updates.password;

    const profile = await (Profile as SINGLE_PROFILE_MODEL).findByIdAndUpdate(
      { _id: userId },
      { $set: updates },
      {
        new: true,
        runValidators: true,
      }
    );

    if (!profile) {
      createErrorResponse(res, `Profile not found.`, 404);
      return;
    }

    if (profile.onBoardingStatus === "complete") {
      // Ensure user has a calendar when onboarding is complete
      try {
        const userName = `${profile.firstname} ${profile.lastname}`.trim();
        await ensureUserHasCalendar(
          profile._id,
          profile.role as 'student' | 'tutor',
          userName,
          profile.timezone
        );
        console.log(`Ensured calendar exists for completed onboarding: ${profile._id}`);
      } catch (calendarError) {
        logError(`Failed to ensure calendar during onboarding completion`, "calendar", {
          userId: profile._id,
          role: profile.role,
          error: calendarError
        });
      }

      if (req.user.role === "tutor") {
        const admin = await Admin.findOne({
          role: "admin",
          isSuperAdmin: true,
        });

        if (admin) await sendMail(admin!.email, approveTutorTemplate());
      }
    } else if (user.onBoardingStatus === "new") {
      if (user.role === "student") {
        try {
          await sendMail(profile.email, guideToFindTutorTemplate(profile));
        } catch (err: any) {
          logError(`Failed to send onboarding mail`, "mail", {
            email: profile.email,
            role: profile.role,
          });
        }
      }
    }

    createOkResponse(res, {
      details: errorDetails,
      success: !errorDetails,
      message: `Profile updated successfully.`,
      data: profile,
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};
