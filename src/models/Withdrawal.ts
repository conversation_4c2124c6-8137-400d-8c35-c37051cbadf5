import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IWithdrawal extends Document {
  tutorId: Types.ObjectId;
  
  // Withdrawal details
  amount: number; // amount in cents
  currency: string;
  
  // Method and destination
  withdrawalMethod: 'bank_transfer' | 'paypal' | 'stripe';
  destinationDetails: {
    // Bank transfer details
    accountNumber?: string;
    routingNumber?: string;
    accountHolderName?: string;
    bankName?: string;
    
    // PayPal details
    paypalEmail?: string;
    
    // Stripe details
    stripeAccountId?: string;
  };
  
  // Status and processing
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  
  // External references
  stripeTransferId?: string;
  paypalBatchId?: string;
  bankTransactionId?: string;
  
  // Fees and final amount
  processingFee: number; // fees charged by payment processor
  finalAmount: number; // amount after processing fees
  
  // Timing
  requestedAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  
  // Metadata
  description?: string;
  failureReason?: string;
  adminNotes?: string;
  
  // Earnings included in this withdrawal
  earningIds: Types.ObjectId[];
  
  createdAt: Date;
  updatedAt: Date;
}

const withdrawalSchema = new Schema<IWithdrawal>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  
  amount: { type: Number, required: true, min: 500 }, // minimum $5
  currency: { type: String, default: 'USD' },
  
  withdrawalMethod: {
    type: String,
    enum: ['bank_transfer', 'paypal', 'stripe'],
    required: true
  },
  
  destinationDetails: {
    accountNumber: String,
    routingNumber: String,
    accountHolderName: String,
    bankName: String,
    paypalEmail: String,
    stripeAccountId: String
  },
  
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  
  stripeTransferId: String,
  paypalBatchId: String,
  bankTransactionId: String,
  
  processingFee: { type: Number, default: 0, min: 0 },
  finalAmount: { type: Number, required: true, min: 0 },
  
  requestedAt: { type: Date, default: () => new Date() },
  processedAt: Date,
  completedAt: Date,
  
  description: String,
  failureReason: String,
  adminNotes: String,
  
  earningIds: [{ type: Schema.Types.ObjectId, ref: 'Earning' }],
  
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Indexes
withdrawalSchema.index({ tutorId: 1, status: 1 });
withdrawalSchema.index({ tutorId: 1, requestedAt: -1 });
withdrawalSchema.index({ status: 1, requestedAt: 1 });

// Pre-save middleware
withdrawalSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate final amount if not set
  if (!this.finalAmount && this.amount) {
    this.finalAmount = this.amount - this.processingFee;
  }
  
  next();
});

// Instance methods
withdrawalSchema.methods.markAsProcessing = function() {
  this.status = 'processing';
  this.processedAt = new Date();
  return this.save();
};

withdrawalSchema.methods.markAsCompleted = function(transactionId?: string) {
  this.status = 'completed';
  this.completedAt = new Date();
  
  if (transactionId) {
    switch (this.withdrawalMethod) {
      case 'stripe':
        this.stripeTransferId = transactionId;
        break;
      case 'paypal':
        this.paypalBatchId = transactionId;
        break;
      case 'bank_transfer':
        this.bankTransactionId = transactionId;
        break;
    }
  }
  
  return this.save();
};

withdrawalSchema.methods.markAsFailed = function(reason: string) {
  this.status = 'failed';
  this.failureReason = reason;
  return this.save();
};

withdrawalSchema.methods.cancel = function(reason?: string) {
  this.status = 'cancelled';
  if (reason) {
    this.adminNotes = reason;
  }
  return this.save();
};

// Static methods
withdrawalSchema.statics.createWithdrawal = async function(tutorId: Types.ObjectId, amount: number, method: string, destinationDetails: any, earningIds: Types.ObjectId[]) {
  // Calculate processing fee based on method
  let processingFee = 0;
  switch (method) {
    case 'stripe':
      processingFee = Math.round(amount * 0.025); // 2.5% for Stripe
      break;
    case 'paypal':
      processingFee = Math.round(amount * 0.02); // 2% for PayPal
      break;
    case 'bank_transfer':
      processingFee = 300; // $3 flat fee for bank transfers
      break;
  }
  
  const finalAmount = amount - processingFee;
  
  return this.create({
    tutorId,
    amount,
    withdrawalMethod: method,
    destinationDetails,
    processingFee,
    finalAmount,
    earningIds,
    status: 'pending'
  });
};

withdrawalSchema.statics.getTutorWithdrawals = function(tutorId: Types.ObjectId, limit: number = 10) {
  return this.find({ tutorId })
    .sort({ requestedAt: -1 })
    .limit(limit)
    .populate('earningIds', 'lessonId grossAmount netEarnings lessonDate');
};

export const Withdrawal = mongoose.model<IWithdrawal>('Withdrawal', withdrawalSchema);
