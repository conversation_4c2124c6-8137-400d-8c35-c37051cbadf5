import mongoose, { Document, Schema, Types } from 'mongoose';

// Interface for individual earnings from lessons
export interface IEarning extends Document {
  tutorId: Types.ObjectId;
  lessonId: Types.ObjectId;
  studentId: Types.ObjectId;
  subscriptionId?: Types.ObjectId;
  
  // Lesson details
  lessonDate: Date;
  lessonDuration: number; // in minutes
  hourlyRate: number; // tutor's rate per hour in cents
  
  // Financial breakdown
  grossAmount: number; // total lesson cost in cents
  platformFeeRate: number; // platform fee percentage (e.g., 20)
  platformFee: number; // platform fee amount in cents
  netEarnings: number; // amount tutor receives in cents
  
  // Payment status
  status: 'pending' | 'available' | 'withdrawn' | 'disputed';
  escrowId?: Types.ObjectId;
  
  // Metadata
  currency: string;
  paymentMethod?: string;
  processingFee?: number; // payment processing fees
  
  createdAt: Date;
  updatedAt: Date;
}

// Interface for tutor's total earnings summary
export interface ITutorEarnings extends Document {
  tutorId: Types.ObjectId;
  
  // Total earnings breakdown
  totalGrossEarnings: number; // total before fees
  totalPlatformFees: number; // total platform fees deducted
  totalNetEarnings: number; // total after platform fees
  totalWithdrawn: number; // total amount withdrawn
  availableBalance: number; // available for withdrawal
  pendingBalance: number; // pending from recent lessons
  
  // Statistics
  totalLessonsCompleted: number;
  totalHoursTaught: number;
  averageHourlyRate: number;
  
  // Withdrawal settings
  minimumWithdrawal: number; // minimum amount for withdrawal
  autoWithdrawal: boolean; // auto-withdraw when threshold reached
  autoWithdrawalThreshold?: number;
  
  // Bank/payment details
  withdrawalMethod: 'bank_transfer' | 'paypal' | 'stripe';
  bankDetails?: {
    accountNumber: string;
    routingNumber: string;
    accountHolderName: string;
    bankName: string;
  };
  paypalEmail?: string;
  stripeAccountId?: string;
  
  lastUpdated: Date;
  createdAt: Date;
}

const earningSchema = new Schema<IEarning>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  lessonId: { type: Schema.Types.ObjectId, ref: 'Lesson', required: true },
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  subscriptionId: { type: Schema.Types.ObjectId, ref: 'Subscription' },
  
  lessonDate: { type: Date, required: true },
  lessonDuration: { type: Number, required: true, min: 15 },
  hourlyRate: { type: Number, required: true, min: 0 },
  
  grossAmount: { type: Number, required: true, min: 0 },
  platformFeeRate: { type: Number, required: true, min: 0, max: 100, default: 20 },
  platformFee: { type: Number, required: true, min: 0 },
  netEarnings: { type: Number, required: true, min: 0 },
  
  status: {
    type: String,
    enum: ['pending', 'available', 'withdrawn', 'disputed'],
    default: 'pending'
  },
  escrowId: { type: Schema.Types.ObjectId, ref: 'Escrow' },
  
  currency: { type: String, default: 'USD' },
  paymentMethod: String,
  processingFee: { type: Number, default: 0 },
  
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

const tutorEarningsSchema = new Schema<ITutorEarnings>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true, unique: true },
  
  totalGrossEarnings: { type: Number, default: 0, min: 0 },
  totalPlatformFees: { type: Number, default: 0, min: 0 },
  totalNetEarnings: { type: Number, default: 0, min: 0 },
  totalWithdrawn: { type: Number, default: 0, min: 0 },
  availableBalance: { type: Number, default: 0, min: 0 },
  pendingBalance: { type: Number, default: 0, min: 0 },
  
  totalLessonsCompleted: { type: Number, default: 0, min: 0 },
  totalHoursTaught: { type: Number, default: 0, min: 0 },
  averageHourlyRate: { type: Number, default: 0, min: 0 },
  
  minimumWithdrawal: { type: Number, default: 2000, min: 500 }, // $20 minimum
  autoWithdrawal: { type: Boolean, default: false },
  autoWithdrawalThreshold: { type: Number, min: 1000 },
  
  withdrawalMethod: {
    type: String,
    enum: ['bank_transfer', 'paypal', 'stripe'],
    default: 'stripe'
  },
  bankDetails: {
    accountNumber: String,
    routingNumber: String,
    accountHolderName: String,
    bankName: String
  },
  paypalEmail: String,
  stripeAccountId: String,
  
  lastUpdated: { type: Date, default: () => new Date() },
  createdAt: { type: Date, default: () => new Date() }
});

// Indexes for better performance
earningSchema.index({ tutorId: 1, status: 1 });
earningSchema.index({ lessonId: 1 });
earningSchema.index({ tutorId: 1, lessonDate: -1 });
earningSchema.index({ status: 1, createdAt: -1 });

tutorEarningsSchema.index({ tutorId: 1 });

// Pre-save middleware
earningSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate net earnings if not set
  if (!this.netEarnings && this.grossAmount && this.platformFeeRate) {
    this.platformFee = Math.round(this.grossAmount * (this.platformFeeRate / 100));
    this.netEarnings = this.grossAmount - this.platformFee - (this.processingFee || 0);
  }
  
  next();
});

tutorEarningsSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

// Instance methods for earnings
earningSchema.methods.markAsAvailable = function() {
  this.status = 'available';
  return this.save();
};

earningSchema.methods.markAsWithdrawn = function() {
  this.status = 'withdrawn';
  return this.save();
};

// Static methods for earnings
earningSchema.statics.createFromLesson = async function(lessonData: any) {
  const { tutorId, lessonId, studentId, subscriptionId, lessonDate, duration, hourlyRate, totalAmount } = lessonData;
  
  const grossAmount = totalAmount;
  const platformFeeRate = 20; // 20% platform fee
  const platformFee = Math.round(grossAmount * (platformFeeRate / 100));
  const netEarnings = grossAmount - platformFee;
  
  return this.create({
    tutorId,
    lessonId,
    studentId,
    subscriptionId,
    lessonDate,
    lessonDuration: duration,
    hourlyRate,
    grossAmount,
    platformFeeRate,
    platformFee,
    netEarnings,
    status: 'pending'
  });
};

export const Earning = mongoose.model<IEarning>('Earning', earningSchema);
export const TutorEarnings = mongoose.model<ITutorEarnings>('TutorEarnings', tutorEarningsSchema);
