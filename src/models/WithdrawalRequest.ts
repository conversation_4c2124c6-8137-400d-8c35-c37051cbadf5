import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IWithdrawalRequest extends Document {
  tutorId: Types.ObjectId;
  
  // Request details
  requestedAmount: number; // amount in cents
  currency: string;
  
  // Withdrawal method and destination
  withdrawalMethod: 'bank_transfer' | 'paypal' | 'stripe' | 'mobile_money';
  destinationDetails: {
    // Bank transfer details
    accountNumber?: string;
    routingNumber?: string;
    accountHolderName?: string;
    bankName?: string;
    swiftCode?: string;
    
    // PayPal details
    paypalEmail?: string;
    
    // Stripe details
    stripeAccountId?: string;
    
    // Mobile money details
    phoneNumber?: string;
    provider?: string; // MTN, Airtel, etc.
  };
  
  // Request status
  status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed' | 'failed' | 'cancelled';
  
  // Admin handling
  reviewedBy?: Types.ObjectId; // Admin who reviewed
  reviewedAt?: Date;
  adminNotes?: string;
  rejectionReason?: string;
  
  // Processing details
  processingFee: number; // estimated processing fee
  finalAmount: number; // amount after fees
  
  // External transaction references
  stripeTransferId?: string;
  paypalBatchId?: string;
  bankTransactionId?: string;
  
  // Timing
  requestedAt: Date;
  approvedAt?: Date;
  processedAt?: Date;
  completedAt?: Date;
  
  // Metadata
  tutorNote?: string; // note from tutor
  failureReason?: string;
  
  // Earnings breakdown
  availableBalance: number; // tutor's available balance at time of request
  earningsIncluded: {
    earningId: Types.ObjectId;
    lessonId: Types.ObjectId;
    amount: number;
    lessonDate: Date;
  }[];
  
  createdAt: Date;
  updatedAt: Date;
}

const withdrawalRequestSchema = new Schema<IWithdrawalRequest>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  
  requestedAmount: { type: Number, required: true, min: 1000 }, // minimum $10
  currency: { type: String, default: 'USD' },
  
  withdrawalMethod: {
    type: String,
    enum: ['bank_transfer', 'paypal', 'stripe', 'mobile_money'],
    required: true
  },
  
  destinationDetails: {
    accountNumber: String,
    routingNumber: String,
    accountHolderName: String,
    bankName: String,
    swiftCode: String,
    paypalEmail: String,
    stripeAccountId: String,
    phoneNumber: String,
    provider: String
  },
  
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  
  reviewedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
  reviewedAt: Date,
  adminNotes: String,
  rejectionReason: String,
  
  processingFee: { type: Number, default: 0, min: 0 },
  finalAmount: { type: Number, required: true, min: 0 },
  
  stripeTransferId: String,
  paypalBatchId: String,
  bankTransactionId: String,
  
  requestedAt: { type: Date, default: () => new Date() },
  approvedAt: Date,
  processedAt: Date,
  completedAt: Date,
  
  tutorNote: String,
  failureReason: String,
  
  availableBalance: { type: Number, required: true, min: 0 },
  earningsIncluded: [{
    earningId: { type: Schema.Types.ObjectId, ref: 'Earning', required: true },
    lessonId: { type: Schema.Types.ObjectId, ref: 'Lesson', required: true },
    amount: { type: Number, required: true, min: 0 },
    lessonDate: { type: Date, required: true }
  }],
  
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Indexes for better performance
withdrawalRequestSchema.index({ tutorId: 1, status: 1 });
withdrawalRequestSchema.index({ status: 1, requestedAt: 1 });
withdrawalRequestSchema.index({ reviewedBy: 1, reviewedAt: -1 });
withdrawalRequestSchema.index({ tutorId: 1, requestedAt: -1 });

// Pre-save middleware
withdrawalRequestSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate processing fee and final amount if not set
  if (!this.finalAmount && this.requestedAmount) {
    let processingFee = 0;
    
    switch (this.withdrawalMethod) {
      case 'stripe':
        processingFee = Math.round(this.requestedAmount * 0.025); // 2.5%
        break;
      case 'paypal':
        processingFee = Math.round(this.requestedAmount * 0.02); // 2%
        break;
      case 'bank_transfer':
        processingFee = 500; // $5 flat fee
        break;
      case 'mobile_money':
        processingFee = Math.round(this.requestedAmount * 0.015); // 1.5%
        break;
    }
    
    this.processingFee = processingFee;
    this.finalAmount = this.requestedAmount - processingFee;
  }
  
  next();
});

// Instance methods
withdrawalRequestSchema.methods.approve = function(adminId: Types.ObjectId, notes?: string) {
  this.status = 'approved';
  this.reviewedBy = adminId;
  this.reviewedAt = new Date();
  this.approvedAt = new Date();
  if (notes) this.adminNotes = notes;
  return this.save();
};

withdrawalRequestSchema.methods.reject = function(adminId: Types.ObjectId, reason: string, notes?: string) {
  this.status = 'rejected';
  this.reviewedBy = adminId;
  this.reviewedAt = new Date();
  this.rejectionReason = reason;
  if (notes) this.adminNotes = notes;
  return this.save();
};

withdrawalRequestSchema.methods.markAsProcessing = function() {
  this.status = 'processing';
  this.processedAt = new Date();
  return this.save();
};

withdrawalRequestSchema.methods.markAsCompleted = function(transactionId?: string) {
  this.status = 'completed';
  this.completedAt = new Date();
  
  if (transactionId) {
    switch (this.withdrawalMethod) {
      case 'stripe':
        this.stripeTransferId = transactionId;
        break;
      case 'paypal':
        this.paypalBatchId = transactionId;
        break;
      case 'bank_transfer':
      case 'mobile_money':
        this.bankTransactionId = transactionId;
        break;
    }
  }
  
  return this.save();
};

withdrawalRequestSchema.methods.markAsFailed = function(reason: string) {
  this.status = 'failed';
  this.failureReason = reason;
  return this.save();
};

// Add static methods to interface
interface IWithdrawalRequestModel extends mongoose.Model<IWithdrawalRequest> {
  createRequest(
    tutorId: Types.ObjectId,
    amount: number,
    method: string,
    destinationDetails: any,
    availableBalance: number,
    earningsIncluded: any[],
    tutorNote?: string
  ): Promise<IWithdrawalRequest>;

  getPendingRequests(limit?: number): Promise<IWithdrawalRequest[]>;
  getTutorRequests(tutorId: Types.ObjectId, limit?: number): Promise<IWithdrawalRequest[]>;
}

// Static methods
withdrawalRequestSchema.statics.createRequest = async function(
  tutorId: Types.ObjectId,
  amount: number,
  method: string,
  destinationDetails: any,
  availableBalance: number,
  earningsIncluded: any[],
  tutorNote?: string
) {
  return this.create({
    tutorId,
    requestedAmount: amount,
    withdrawalMethod: method,
    destinationDetails,
    availableBalance,
    earningsIncluded,
    tutorNote,
    status: 'pending'
  });
};

withdrawalRequestSchema.statics.getPendingRequests = function(limit: number = 50) {
  return this.find({ status: 'pending' })
    .populate('tutorId', 'firstname lastname email')
    .sort({ requestedAt: 1 }) // oldest first
    .limit(limit);
};

withdrawalRequestSchema.statics.getTutorRequests = function(tutorId: Types.ObjectId, limit: number = 20) {
  return this.find({ tutorId })
    .sort({ requestedAt: -1 })
    .limit(limit);
};

export const WithdrawalRequest = mongoose.model<IWithdrawalRequest, IWithdrawalRequestModel>('WithdrawalRequest', withdrawalRequestSchema);
