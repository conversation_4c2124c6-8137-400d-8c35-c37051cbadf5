import { Response } from 'express';
import { Types } from 'mongoose';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { createOkResponse } from '../utils/misc';
import { WithdrawalRequest } from '../models/WithdrawalRequest';
import { Lesson } from '../models/Lesson';
import TransactionModel from '../models/transaction.model';

/**
 * Create a withdrawal request
 */
export const createWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create withdrawal requests', 403);
      return;
    }

    const {
      amount,
      withdrawalMethod,
      destinationDetails,
      tutorNote
    } = req.body;

    const tutorId = req.user._id;

    // Validate required fields
    if (!amount || !withdrawalMethod || !destinationDetails) {
      createErrorResponse(res, 'Amount, withdrawal method, and destination details are required', 400);
      return;
    }

    // Minimum withdrawal amount ($10)
    if (amount < 1000) {
      createErrorResponse(res, 'Minimum withdrawal amount is $10.00', 400);
      return;
    }

    // Calculate available balance
    const completedLessons = await Lesson.find({
      tutorId: tutorId,
      status: 'completed',
      paymentStatus: 'paid'
    }).select('tutorPayout');

    const totalEarnings = completedLessons.reduce((sum, lesson) => sum + (lesson.tutorPayout || 0), 0);

    // Get already withdrawn amount
    const previousWithdrawals = await WithdrawalRequest.find({
      tutorId: tutorId,
      status: { $in: ['approved', 'processing', 'completed'] }
    }).select('finalAmount');

    const totalWithdrawn = previousWithdrawals.reduce((sum, withdrawal) => sum + withdrawal.finalAmount, 0);
    const availableBalance = totalEarnings - totalWithdrawn;

    // Check if tutor has sufficient balance
    if (amount > availableBalance) {
      createErrorResponse(res, `Insufficient balance. Available: $${(availableBalance / 100).toFixed(2)}`, 400);
      return;
    }

    // Check for pending withdrawal requests
    const pendingRequest = await WithdrawalRequest.findOne({
      tutorId: tutorId,
      status: 'pending'
    });

    if (pendingRequest) {
      createErrorResponse(res, 'You already have a pending withdrawal request', 400);
      return;
    }

    // Validate destination details based on method
    const validationError = validateDestinationDetails(withdrawalMethod, destinationDetails);
    if (validationError) {
      createErrorResponse(res, validationError, 400);
      return;
    }

    // Get earnings to include in this withdrawal
    const earningsToInclude = completedLessons
      .filter(lesson => lesson.tutorPayout && lesson.tutorPayout > 0)
      .map(lesson => ({
        earningId: lesson._id,
        lessonId: lesson._id,
        amount: lesson.tutorPayout || 0,
        lessonDate: lesson.startDateTime
      }))
      .slice(0, Math.ceil(amount / (totalEarnings / completedLessons.length)));

    // Create withdrawal request
    const withdrawalRequest = await WithdrawalRequest.createRequest(
      tutorId,
      amount,
      withdrawalMethod,
      destinationDetails,
      availableBalance,
      earningsToInclude,
      tutorNote
    );

    createOkResponse(res, {
      message: 'Withdrawal request created successfully',
      data: {
        requestId: withdrawalRequest._id,
        requestedAmount: `$${(amount / 100).toFixed(2)}`,
        processingFee: `$${(withdrawalRequest.processingFee / 100).toFixed(2)}`,
        finalAmount: `$${(withdrawalRequest.finalAmount / 100).toFixed(2)}`,
        withdrawalMethod,
        status: withdrawalRequest.status,
        estimatedProcessingTime: getEstimatedProcessingTime(withdrawalMethod)
      }
    });

  } catch (error: any) {
    console.error('Error creating withdrawal request:', error);
    createErrorResponse(res, error.message || 'Failed to create withdrawal request', 500);
  }
};

/**
 * Get tutor's withdrawal requests
 */
export const getTutorWithdrawals = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view withdrawal requests', 403);
      return;
    }

    const tutorId = req.user._id;
    const { limit = 20 } = req.query;

    const withdrawals = await WithdrawalRequest.getTutorRequests(tutorId, parseInt(limit as string));

    const formattedWithdrawals = withdrawals.map(withdrawal => ({
      id: withdrawal._id,
      requestedAmount: `$${(withdrawal.requestedAmount / 100).toFixed(2)}`,
      processingFee: `$${(withdrawal.processingFee / 100).toFixed(2)}`,
      finalAmount: `$${(withdrawal.finalAmount / 100).toFixed(2)}`,
      withdrawalMethod: withdrawal.withdrawalMethod,
      status: withdrawal.status,
      requestedAt: withdrawal.requestedAt,
      reviewedAt: withdrawal.reviewedAt,
      completedAt: withdrawal.completedAt,
      rejectionReason: withdrawal.rejectionReason,
      adminNotes: withdrawal.adminNotes,
      tutorNote: withdrawal.tutorNote,
      transactionId: withdrawal.stripeTransferId || withdrawal.paypalBatchId || withdrawal.bankTransactionId
    }));

    createOkResponse(res, {
      data: {
        withdrawals: formattedWithdrawals,
        total: formattedWithdrawals.length
      }
    });

  } catch (error: any) {
    console.error('Error fetching withdrawal requests:', error);
    createErrorResponse(res, error.message || 'Failed to fetch withdrawal requests', 500);
  }
};

/**
 * Get available balance for withdrawal
 */
export const getAvailableBalance = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view available balance', 403);
      return;
    }

    const tutorId = req.user._id;

    // Calculate total earnings
    const completedLessons = await Lesson.find({
      tutorId: tutorId,
      status: 'completed',
      paymentStatus: 'paid'
    }).select('tutorPayout');

    const totalEarnings = completedLessons.reduce((sum, lesson) => sum + (lesson.tutorPayout || 0), 0);

    // Get total withdrawn
    const withdrawnAmount = await WithdrawalRequest.aggregate([
      {
        $match: {
          tutorId: tutorId as Types.ObjectId,
          status: { $in: ['approved', 'processing', 'completed'] }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$finalAmount' }
        }
      }
    ]);

    const totalWithdrawn = withdrawnAmount[0]?.total || 0;
    const availableBalance = totalEarnings - totalWithdrawn;

    // Check for pending requests
    const pendingRequest = await WithdrawalRequest.findOne({
      tutorId: tutorId,
      status: 'pending'
    });

    createOkResponse(res, {
      data: {
        totalEarnings: `$${(totalEarnings / 100).toFixed(2)}`,
        totalWithdrawn: `$${(totalWithdrawn / 100).toFixed(2)}`,
        availableBalance: `$${(availableBalance / 100).toFixed(2)}`,
        availableBalanceCents: availableBalance,
        minimumWithdrawal: '$10.00',
        hasPendingRequest: !!pendingRequest,
        pendingRequestAmount: pendingRequest ? `$${(pendingRequest.requestedAmount / 100).toFixed(2)}` : null
      }
    });

  } catch (error: any) {
    console.error('Error fetching available balance:', error);
    createErrorResponse(res, error.message || 'Failed to fetch available balance', 500);
  }
};

/**
 * Cancel a pending withdrawal request
 */
export const cancelWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can cancel withdrawal requests', 403);
      return;
    }

    const { requestId } = req.params;
    const tutorId = req.user._id;

    const withdrawalRequest = await WithdrawalRequest.findOne({
      _id: requestId,
      tutorId: tutorId,
      status: 'pending'
    });

    if (!withdrawalRequest) {
      createErrorResponse(res, 'Withdrawal request not found or cannot be cancelled', 404);
      return;
    }

    withdrawalRequest.status = 'cancelled';
    withdrawalRequest.adminNotes = 'Cancelled by tutor';
    await withdrawalRequest.save();

    createOkResponse(res, {
      message: 'Withdrawal request cancelled successfully',
      data: null
    });

  } catch (error: any) {
    console.error('Error cancelling withdrawal request:', error);
    createErrorResponse(res, error.message || 'Failed to cancel withdrawal request', 500);
  }
};

// Helper functions
function validateDestinationDetails(method: string, details: any): string | null {
  switch (method) {
    case 'bank_transfer':
      if (!details.accountNumber || !details.accountHolderName || !details.bankName) {
        return 'Bank transfer requires account number, account holder name, and bank name';
      }
      break;
    case 'paypal':
      if (!details.paypalEmail) {
        return 'PayPal withdrawal requires PayPal email address';
      }
      break;
    case 'mobile_money':
      if (!details.phoneNumber || !details.provider) {
        return 'Mobile money requires phone number and provider';
      }
      break;
    case 'stripe':
      if (!details.stripeAccountId) {
        return 'Stripe withdrawal requires connected Stripe account';
      }
      break;
    default:
      return 'Invalid withdrawal method';
  }
  return null;
}

function getEstimatedProcessingTime(method: string): string {
  switch (method) {
    case 'stripe':
      return '1-2 business days';
    case 'paypal':
      return '1-3 business days';
    case 'bank_transfer':
      return '3-5 business days';
    case 'mobile_money':
      return '1-24 hours';
    default:
      return '1-5 business days';
  }
}

/**
 * Admin: Get all pending withdrawal requests
 */
export const getAdminPendingWithdrawals = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      createErrorResponse(res, 'Only admins can view pending withdrawals', 403);
      return;
    }

    const { limit = 50 } = req.query;

    const pendingRequests = await WithdrawalRequest.getPendingRequests(parseInt(limit as string));

    const formattedRequests = pendingRequests.map(request => ({
      id: request._id,
      tutor: {
        id: (request.tutorId as any)._id,
        name: `${(request.tutorId as any).firstname} ${(request.tutorId as any).lastname}`,
        email: (request.tutorId as any).email
      },
      requestedAmount: `$${(request.requestedAmount / 100).toFixed(2)}`,
      processingFee: `$${(request.processingFee / 100).toFixed(2)}`,
      finalAmount: `$${(request.finalAmount / 100).toFixed(2)}`,
      withdrawalMethod: request.withdrawalMethod,
      destinationDetails: request.destinationDetails,
      requestedAt: request.requestedAt,
      tutorNote: request.tutorNote,
      availableBalance: `$${(request.availableBalance / 100).toFixed(2)}`,
      earningsCount: request.earningsIncluded.length
    }));

    createOkResponse(res, {
      data: {
        pendingRequests: formattedRequests,
        total: formattedRequests.length
      }
    });

  } catch (error: any) {
    console.error('Error fetching pending withdrawals:', error);
    createErrorResponse(res, error.message || 'Failed to fetch pending withdrawals', 500);
  }
};

/**
 * Admin: Approve withdrawal request
 */
export const approveWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      createErrorResponse(res, 'Only admins can approve withdrawal requests', 403);
      return;
    }

    const { requestId } = req.params;
    const { adminNotes } = req.body;

    const withdrawalRequest = await WithdrawalRequest.findOne({
      _id: requestId,
      status: 'pending'
    });

    if (!withdrawalRequest) {
      createErrorResponse(res, 'Withdrawal request not found or already processed', 404);
      return;
    }

    await withdrawalRequest.approve(req.user._id, adminNotes);

    createOkResponse(res, {
      message: 'Withdrawal request approved successfully',
      data: {
        requestId: withdrawalRequest._id,
        status: withdrawalRequest.status,
        approvedAt: withdrawalRequest.approvedAt
      }
    });

  } catch (error: any) {
    console.error('Error approving withdrawal request:', error);
    createErrorResponse(res, error.message || 'Failed to approve withdrawal request', 500);
  }
};

/**
 * Admin: Reject withdrawal request
 */
export const rejectWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      createErrorResponse(res, 'Only admins can reject withdrawal requests', 403);
      return;
    }

    const { requestId } = req.params;
    const { rejectionReason, adminNotes } = req.body;

    if (!rejectionReason) {
      createErrorResponse(res, 'Rejection reason is required', 400);
      return;
    }

    const withdrawalRequest = await WithdrawalRequest.findOne({
      _id: requestId,
      status: 'pending'
    });

    if (!withdrawalRequest) {
      createErrorResponse(res, 'Withdrawal request not found or already processed', 404);
      return;
    }

    await withdrawalRequest.reject(req.user._id, rejectionReason, adminNotes);

    createOkResponse(res, {
      message: 'Withdrawal request rejected',
      data: {
        requestId: withdrawalRequest._id,
        status: withdrawalRequest.status,
        rejectionReason: withdrawalRequest.rejectionReason
      }
    });

  } catch (error: any) {
    console.error('Error rejecting withdrawal request:', error);
    createErrorResponse(res, error.message || 'Failed to reject withdrawal request', 500);
  }
};
