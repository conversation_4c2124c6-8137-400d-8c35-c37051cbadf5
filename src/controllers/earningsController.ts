import { Response } from 'express';
import { Types } from 'mongoose';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { createOkResponse } from '../utils/misc';
import { Lesson } from '../models/Lesson';
import { WithdrawalRequest } from '../models/WithdrawalRequest';
import Subscription from '../models/subscription.model';
import TransactionModel from '../models/transaction.model';

// Interface for tutor earnings summary
interface TutorEarningsSummary {
  totalGrossEarnings: number;
  totalPlatformFees: number;
  totalNetEarnings: number;
  totalWithdrawn: number;
  availableBalance: number;
  pendingBalance: number;
  totalLessonsCompleted: number;
  totalHoursTaught: number;
  averageHourlyRate: number;
}

/**
 * Process lesson completion and create earnings record
 */
export const processLessonEarnings = async (lessonId: Types.ObjectId): Promise<void> => {
  try {
    const lesson = await Lesson.findById(lessonId)
      .populate('tutorId', 'basePrice')
      .populate('subscriptionId', 'monthlyPrice lessonsPerWeek');

    if (!lesson || lesson.status !== 'completed') {
      throw new Error('Lesson not found or not completed');
    }

    if (lesson.tutorEarningId) {
      // Earnings already processed
      return;
    }

    // Calculate earnings
    const grossAmount = lesson.price || 0;
    const platformFeeRate = 20; // 20% platform fee
    const platformFee = Math.round(grossAmount * (platformFeeRate / 100));
    const tutorPayout = grossAmount - platformFee;

    // Update lesson with earnings info
    lesson.platformFee = platformFee;
    lesson.tutorPayout = tutorPayout;
    lesson.paymentDeductedAt = new Date();
    lesson.paymentStatus = 'paid';

    await lesson.save();

    // Create transaction record for tutor earnings
    await TransactionModel.create({
      userId: lesson.tutorId,
      amount: tutorPayout,
      type: 'lesson_payout',
      status: 'completed',
      description: `Earnings from lesson: ${lesson.title}`
    });

    console.log(`Processed earnings for lesson ${lessonId}: $${tutorPayout / 100} to tutor`);
  } catch (error) {
    console.error('Error processing lesson earnings:', error);
    throw error;
  }
};

/**
 * Get tutor's earnings summary
 */
export const getTutorEarnings = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view earnings', 403);
      return;
    }

    const tutorId = req.user._id;

    // Get completed lessons with earnings
    const completedLessons = await Lesson.find({
      tutorId: tutorId,
      status: 'completed',
      paymentStatus: 'paid'
    }).select('price platformFee tutorPayout duration startDateTime paymentDeductedAt');

    // Calculate totals
    const totalGrossEarnings = completedLessons.reduce((sum, lesson) => sum + (lesson.price || 0), 0);
    const totalPlatformFees = completedLessons.reduce((sum, lesson) => sum + (lesson.platformFee || 0), 0);
    const totalNetEarnings = completedLessons.reduce((sum, lesson) => sum + (lesson.tutorPayout || 0), 0);
    const totalHoursTaught = completedLessons.reduce((sum, lesson) => sum + (lesson.duration || 0), 0) / 60;

    // Get total withdrawn amount
    const withdrawnTransactions = await TransactionModel.find({
      userId: tutorId,
      type: 'lesson_payout',
      status: 'completed'
    });

    const totalWithdrawn = await WithdrawalRequest.aggregate([
      {
        $match: {
          tutorId: tutorId as Types.ObjectId,
          status: 'completed'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$finalAmount' }
        }
      }
    ]);

    const totalWithdrawnAmount = totalWithdrawn[0]?.total || 0;
    const availableBalance = totalNetEarnings - totalWithdrawnAmount;

    // Get pending earnings (lessons completed but payment not yet processed)
    const pendingLessons = await Lesson.find({
      tutorId: tutorId,
      status: 'completed',
      paymentStatus: { $in: ['pending', 'free_trial'] }
    });

    const pendingBalance = pendingLessons.reduce((sum, lesson) => {
      const grossAmount = lesson.price || 0;
      const platformFee = Math.round(grossAmount * 0.2);
      return sum + (grossAmount - platformFee);
    }, 0);

    const averageHourlyRate = totalHoursTaught > 0 ? Math.round((totalGrossEarnings / totalHoursTaught) / 100 * 100) / 100 : 0;

    const earningsSummary: TutorEarningsSummary = {
      totalGrossEarnings,
      totalPlatformFees,
      totalNetEarnings,
      totalWithdrawn: totalWithdrawnAmount,
      availableBalance,
      pendingBalance,
      totalLessonsCompleted: completedLessons.length,
      totalHoursTaught: Math.round(totalHoursTaught * 100) / 100,
      averageHourlyRate
    };

    createOkResponse(res, {
      data: {
        summary: earningsSummary,
        recentEarnings: completedLessons.slice(-10).map(lesson => ({
          id: lesson._id,
          date: lesson.startDateTime,
          grossAmount: lesson.price,
          platformFee: lesson.platformFee,
          netEarning: lesson.tutorPayout,
          duration: lesson.duration,
          paymentDeductedAt: lesson.paymentDeductedAt
        })),
        formattedSummary: {
          totalGrossEarnings: `$${(totalGrossEarnings / 100).toFixed(2)}`,
          totalPlatformFees: `$${(totalPlatformFees / 100).toFixed(2)}`,
          totalNetEarnings: `$${(totalNetEarnings / 100).toFixed(2)}`,
          availableBalance: `$${(availableBalance / 100).toFixed(2)}`,
          pendingBalance: `$${(pendingBalance / 100).toFixed(2)}`,
          averageHourlyRate: `$${averageHourlyRate}/hour`
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching tutor earnings:', error);
    createErrorResponse(res, error.message || 'Failed to fetch earnings', 500);
  }
};

/**
 * Get detailed earnings breakdown
 */
export const getEarningsBreakdown = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view earnings breakdown', 403);
      return;
    }

    const tutorId = req.user._id;
    const { startDate, endDate, limit = 50 } = req.query;

    // Build date filter
    const dateFilter: any = {
      tutorId: tutorId,
      status: 'completed',
      paymentStatus: 'paid'
    };

    if (startDate || endDate) {
      dateFilter.startDateTime = {};
      if (startDate) dateFilter.startDateTime.$gte = new Date(startDate as string);
      if (endDate) dateFilter.startDateTime.$lte = new Date(endDate as string);
    }

    const earnings = await Lesson.find(dateFilter)
      .populate('studentId', 'firstname lastname')
      .populate('subscriptionId', 'planType')
      .sort({ startDateTime: -1 })
      .limit(parseInt(limit as string))
      .select('title subject startDateTime duration price platformFee tutorPayout studentId subscriptionId paymentDeductedAt');

    const formattedEarnings = earnings.map(lesson => ({
      id: lesson._id,
      lessonTitle: lesson.title,
      subject: lesson.subject,
      date: lesson.startDateTime,
      duration: lesson.duration,
      student: {
        id: (lesson.studentId as any)?._id,
        name: `${(lesson.studentId as any)?.firstname} ${(lesson.studentId as any)?.lastname}`
      },
      grossAmount: lesson.price,
      platformFee: lesson.platformFee,
      netEarning: lesson.tutorPayout,
      hourlyRate: lesson.duration ? Math.round(((lesson.price || 0) / (lesson.duration / 60)) / 100 * 100) / 100 : 0,
      paymentDate: lesson.paymentDeductedAt,
      subscriptionType: (lesson.subscriptionId as any)?.planType
    }));

    createOkResponse(res, {
      data: {
        earnings: formattedEarnings,
        total: formattedEarnings.length,
        summary: {
          totalGross: formattedEarnings.reduce((sum, e) => sum + (e.grossAmount || 0), 0),
          totalNet: formattedEarnings.reduce((sum, e) => sum + (e.netEarning || 0), 0),
          totalFees: formattedEarnings.reduce((sum, e) => sum + (e.platformFee || 0), 0),
          totalHours: formattedEarnings.reduce((sum, e) => sum + (e.duration || 0), 0) / 60
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching earnings breakdown:', error);
    createErrorResponse(res, error.message || 'Failed to fetch earnings breakdown', 500);
  }
};

/**
 * Automatically process earnings when lesson is completed
 */
export const autoProcessEarnings = async (lessonId: Types.ObjectId): Promise<void> => {
  try {
    await processLessonEarnings(lessonId);
  } catch (error) {
    console.error(`Failed to auto-process earnings for lesson ${lessonId}:`, error);
  }
};
