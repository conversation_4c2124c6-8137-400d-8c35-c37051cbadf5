import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calendar';
import { Lesson } from '../models/Lesson';
import { Schedule } from '../models/Schedule';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

export enum CalendarViewType {
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
  AGENDA = 'agenda'
}

/**
 * Get calendar view with basic calendar information
 */
export const getCalendarView = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const {
      viewType = CalendarViewType.MONTH,
      startDate,
      endDate,
      timezone = 'UTC',
      calendarIds
    } = req.query;

    // Validate and parse dates
    const start = startDate ? new Date(startDate as string) : getDefaultStartDate(viewType as CalendarViewType);
    const end = endDate ? new Date(endDate as string) : getDefaultEndDate(start, viewType as CalendarViewType);

    // Build calendar query based on user role
    let calendarQuery: any = {};
    if (req.user.role === 'tutor') {
      calendarQuery.ownerId = req.user._id;
      calendarQuery.ownerType = 'tutor';
    } else if (req.user.role === 'student') {
      // Students can see their own calendars and shared tutor calendars
      calendarQuery = {
        $or: [
          { ownerId: req.user._id, ownerType: 'student' },
          { ownerType: 'tutor', isShared: true, isActive: true }
        ]
      };
    }

    // Filter by specific calendars if provided
    if (calendarIds) {
      const ids = Array.isArray(calendarIds) ? calendarIds : [calendarIds];
      calendarQuery._id = { $in: ids.map(id => new Types.ObjectId(id as string)) };
    }

    // Get calendars
    const calendars = await Calendar.find(calendarQuery)
      .populate('ownerId', 'firstname lastname email avatar')
      .populate('defaultScheduleId');

    if (calendars.length === 0) {
      res.json({
        success: true,
        data: {
          calendars: [],
          lessons: [],
          viewInfo: {
            viewType,
            startDate: start,
            endDate: end,
            timezone
          }
        }
      });
      return;
    }

    // Get lessons for the date range
    let lessonQuery: any = {
      startDateTime: { $gte: start, $lte: end }
    };

    // Filter lessons based on user role and calendars
    if (req.user.role === 'student') {
      lessonQuery.studentId = req.user._id;
    } else if (req.user.role === 'tutor') {
      lessonQuery.tutorId = req.user._id;
    }

    const lessons = await Lesson.find(lessonQuery)
      .populate('studentId', 'firstname lastname email avatar')
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('tutorCalendarId', 'name color')
      .populate('studentCalendarId', 'name color')
      .sort({ startDateTime: 1 });

    // Format lessons for calendar view
    const formattedLessons = lessons.map(lesson => ({
      id: lesson._id,
      title: lesson.title,
      start: lesson.startDateTime,
      end: lesson.endDateTime,
      allDay: false,
      status: lesson.status,
      lessonType: lesson.lessonType,
      isTrialLesson: lesson.isTrialLesson,
      subject: lesson.subject,
      description: lesson.description,
      student: lesson.studentId,
      tutor: lesson.tutorId,
      calendar: req.user?.role === 'student' ? lesson.studentCalendarId : lesson.tutorCalendarId,
      duration: lesson.duration,
      price: lesson.price,
      paymentStatus: lesson.paymentStatus,
      canReschedule: lesson.status === 'scheduled' && new Date(lesson.startDateTime) > new Date(Date.now() + 24 * 60 * 60 * 1000),
      canCancel: lesson.status === 'scheduled' && new Date(lesson.startDateTime) > new Date(Date.now() + 24 * 60 * 60 * 1000)
    }));

    // Generate view-specific data structure
    const viewData = generateViewData(formattedLessons, start, end, viewType as CalendarViewType);

    res.json({
      success: true,
      data: {
        calendars: calendars.map(cal => ({
          id: cal._id,
          name: cal.name,
          color: cal.color,
          owner: cal.ownerId,
          ownerType: cal.ownerType,
          isShared: cal.isShared,
          isActive: cal.isActive,
          hasSchedule: cal.hasSchedule,
          timezone: cal.timezone
        })),
        lessons: formattedLessons,
        viewData,
        viewInfo: {
          viewType,
          startDate: start,
          endDate: end,
          timezone,
          totalLessons: formattedLessons.length,
          upcomingLessons: formattedLessons.filter(l => new Date(l.start) > new Date() && l.status === 'scheduled').length,
          completedLessons: formattedLessons.filter(l => l.status === 'completed').length
        }
      }
    });

  } catch (error) {
    console.error('Error fetching calendar view:', error);
    createErrorResponse(res, 'Failed to fetch calendar view', 500);
  }
};

/**
 * Get default start date based on view type
 */
function getDefaultStartDate(viewType: CalendarViewType): Date {
  const now = new Date();
  
  switch (viewType) {
    case CalendarViewType.MONTH:
      return new Date(now.getFullYear(), now.getMonth(), 1);
    case CalendarViewType.WEEK:
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      return startOfWeek;
    case CalendarViewType.DAY:
      const startOfDay = new Date(now);
      startOfDay.setHours(0, 0, 0, 0);
      return startOfDay;
    case CalendarViewType.AGENDA:
      return new Date(now);
    default:
      return new Date(now);
  }
}

/**
 * Get default end date based on view type and start date
 */
function getDefaultEndDate(startDate: Date, viewType: CalendarViewType): Date {
  const end = new Date(startDate);
  
  switch (viewType) {
    case CalendarViewType.MONTH:
      end.setMonth(end.getMonth() + 1);
      end.setDate(0); // Last day of the month
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.WEEK:
      end.setDate(end.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.DAY:
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.AGENDA:
      end.setDate(end.getDate() + 30); // Next 30 days
      return end;
    default:
      return new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
  }
}

/**
 * Generate view-specific data structure
 */
function generateViewData(lessons: any[], startDate: Date, endDate: Date, viewType: CalendarViewType): any {
  switch (viewType) {
    case CalendarViewType.MONTH:
      return generateMonthViewData(lessons, startDate, endDate);
    case CalendarViewType.WEEK:
      return generateWeekViewData(lessons, startDate);
    case CalendarViewType.DAY:
      return generateDayViewData(lessons, startDate);
    case CalendarViewType.AGENDA:
      return generateAgendaViewData(lessons, startDate, endDate);
    default:
      return { viewType, startDate, endDate, lessons };
  }
}

/**
 * Generate month view data with days grid
 */
function generateMonthViewData(lessons: any[], startDate: Date, endDate: Date): any {
  const weeks = [];
  const current = new Date(startDate);

  // Start from the beginning of the week containing the first day of the month
  current.setDate(current.getDate() - current.getDay());

  while (current <= endDate) {
    const week = [];
    for (let i = 0; i < 7; i++) {
      const dayLessons = lessons.filter(lesson => {
        const lessonDate = new Date(lesson.start);
        return lessonDate.toDateString() === current.toDateString();
      });

      week.push({
        date: new Date(current),
        lessons: dayLessons,
        isCurrentMonth: current.getMonth() === startDate.getMonth(),
        isToday: current.toDateString() === new Date().toDateString()
      });

      current.setDate(current.getDate() + 1);
    }
    weeks.push(week);
  }

  return { weeks };
}

/**
 * Generate week view data with time slots
 */
function generateWeekViewData(lessons: any[], startDate: Date): any {
  const days = [];
  const current = new Date(startDate);

  for (let i = 0; i < 7; i++) {
    const dayLessons = lessons.filter(lesson => {
      const lessonDate = new Date(lesson.start);
      return lessonDate.toDateString() === current.toDateString();
    });

    days.push({
      date: new Date(current),
      lessons: dayLessons.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()),
      isToday: current.toDateString() === new Date().toDateString()
    });

    current.setDate(current.getDate() + 1);
  }

  return { days };
}

/**
 * Generate day view data with hourly time slots
 */
function generateDayViewData(lessons: any[], date: Date): any {
  const hours = [];

  for (let hour = 0; hour < 24; hour++) {
    const hourStart = new Date(date);
    hourStart.setHours(hour, 0, 0, 0);

    const hourEnd = new Date(date);
    hourEnd.setHours(hour, 59, 59, 999);

    const hourLessons = lessons.filter(lesson => {
      const lessonStart = new Date(lesson.start);
      const lessonEnd = new Date(lesson.end);

      return (lessonStart >= hourStart && lessonStart <= hourEnd) ||
             (lessonEnd >= hourStart && lessonEnd <= hourEnd) ||
             (lessonStart <= hourStart && lessonEnd >= hourEnd);
    });

    hours.push({
      hour,
      time: hourStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      lessons: hourLessons
    });
  }

  return { date, hours };
}

/**
 * Generate agenda view data grouped by date
 */
function generateAgendaViewData(lessons: any[], startDate: Date, endDate: Date): any {
  const groupedLessons = lessons.reduce((groups, lesson) => {
    const date = new Date(lesson.start).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(lesson);
    return groups;
  }, {} as Record<string, any[]>);

  const agenda = Object.keys(groupedLessons)
    .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
    .map(dateStr => ({
      date: new Date(dateStr),
      lessons: groupedLessons[dateStr].sort((a: any, b: any) => new Date(a.start).getTime() - new Date(b.start).getTime()),
      isToday: dateStr === new Date().toDateString()
    }));

  return { agenda };
}
