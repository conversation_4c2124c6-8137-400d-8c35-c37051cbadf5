import { Request, Response } from "express";
import { comparePassword, generateToken, hashPassword } from "../utils/hashing";
import { createOkResponse } from "../utils/misc";
import {
  createErrorResponse,
  getErrorResponse,
} from "../middlewares/errorHandler";
import { sendMail, sendResetCode } from "../utils/mailer";
import crypto from "crypto";
import { passwordMatchCondition } from "../utils/validation";
import {
  AUTH_PROVIDERS,
  DEFAULT_AUTH_PROVIDER,
  ERROR_FORBIDDEN_ACCESS,
  ERROR_INVALID_ROLE,
} from "../config/constants";
import { PROFILE_REGISTRATION_ROLES } from "../models/profile";
import {
  getProfile,
  getProfileModelByRole,
  updateProfileMedias,
  USER_PROFILE_TYPE,
} from "../utils/profile";
import { logError } from "../utils/error";
import {
  studentWelcomeTemplate,
  tutorWelcomeTemplate,
} from "../utils/email-templates";
import { createDefaultCalendar } from "../utils/calendarUtils";

const registerHook = async (
  req: Request,
  res: Response
): Promise<USER_PROFILE_TYPE> => {
  try {
    const body = req.body;

    const provider = body.provider || DEFAULT_AUTH_PROVIDER;

    if (!AUTH_PROVIDERS.includes(provider)) {
      throw getErrorResponse(
        {
          message: "Invalid authentication provider",
          details: {
            allowedAuthProviders: AUTH_PROVIDERS,
          },
        },
        400
      );
    }

    const withPassword = provider === "local";

    if (
      withPassword &&
      (!body.password || !passwordMatchCondition[0].test(body.password))
    ) {
      throw getErrorResponse(passwordMatchCondition[1], 400);
    }

    const profile = await getProfile({
      email: req.body.email,
    });

    if (profile) {
      throw getErrorResponse("Profile already exists", 400);
    }

    const Profile = getProfileModelByRole(body.role);

    if (withPassword) body.password = await hashPassword(body.password);
    else delete body.password;

    await updateProfileMedias(body, null, true);

    const newProfile = (await Profile.create(body)) as any;

    // Create default calendar for the new user
    try {
      const userName = `${newProfile.firstname} ${newProfile.lastname}`.trim();
      const userType = newProfile.role === 'tutor' ? 'tutor' : 'student';

      await createDefaultCalendar({
        userId: newProfile._id,
        userType: userType,
        userName: userName,
        timezone: newProfile.timezone || 'UTC'
      });

      console.log(`Created default calendar for new ${userType}: ${newProfile._id}`);
    } catch (calendarError) {
      logError(`Failed to create default calendar for new user`, "calendar", {
        userId: newProfile._id,
        email: newProfile.email,
        role: newProfile.role,
        error: calendarError
      });
      // Don't fail registration if calendar creation fails
    }

    try {
      const isTutor = newProfile.role === "tutor";

      await sendMail(
        newProfile.email,
        isTutor
          ? tutorWelcomeTemplate(newProfile)
          : studentWelcomeTemplate(newProfile)
      );
    } catch (err) {
      logError(`Failed to send welcome email`, "mail", {
        email: newProfile.email,
        role: newProfile.role,
      });
    }

    return newProfile;
  } catch (err: any) {
    throw err;
  }
};

export const register = async (req: Request, res: Response) => {
  try {
    if (!PROFILE_REGISTRATION_ROLES.includes(req.body.role)) {
      createErrorResponse(res, ERROR_INVALID_ROLE);
      return;
    }

    const user = await registerHook(req, res);

    createOkResponse(res, {
      data: user,
      message: "Profile created successfully.",
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const body = req.body;

    let user = await getProfile({
      email: body.email,
      id: body.id,
    });

    if (body.provider && body.provider !== DEFAULT_AUTH_PROVIDER) {
      if (!user) user = await registerHook(req, res);
    } else if (
      !user?.password ||
      !comparePassword(body.password, user.password)
    ) {
      createErrorResponse(res, "Email or Password is incorrect.", 400);
      return;
    }

    user!.isLoggedIn = true;

    await user!.save();

    createOkResponse(res, {
      message: "Login successful.",
      data: {
        user,
        accessToken: generateToken(
          { id: user!.id, role: user!.role },
          { rememberMe: body.rememberMe }
        ),
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    const user = await getProfile({ email });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }
    const code = crypto.randomInt(100000, 999999).toString();

    user.resetCode = code;

    user.resetCodeExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 min

    await user.save();

    await sendResetCode({ email, id: user.id }, code, user.resetCodeExpires);

    createOkResponse(res, {
      message: "Reset code sent to your email",
      data: { email, id: user.id },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const verifyOTP = async (req: Request, res: Response) => {
  try {
    const { id, code } = req.body;

    const user = await getProfile({ id });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }

    if (
      !user.resetCode ||
      !user.resetCodeExpires ||
      user.resetCode !== code ||
      user.resetCodeExpires < new Date()
    ) {
      createErrorResponse(res, "Invalid or expired reset code", 400);
      return;
    }

    user.resetCode = undefined;

    user.resetCodeExpires = undefined;

    await user.save();

    createOkResponse(res, {
      message: "OTP verified successfully.",
      data: {
        accessToken: generateToken(
          { id: user.id, role: user.role },
          { expiresIn: "10m" }
        ),
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { id, newPassword } = req.body;

    const user = await getProfile({ id });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }

    if (req.user?.id !== user.id) {
      createErrorResponse(res, ERROR_FORBIDDEN_ACCESS);
      return;
    }

    user.password = await hashPassword(newPassword);

    user.resetCode = undefined;
    user.resetCodeExpires = undefined;

    await user.save();

    createOkResponse(res, "Password reset successfully");

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const logout = async (req: Request, res: Response) => {
  try {
    const user = req.user;

    user.isLoggedIn = false;

    await user.save();

    createOkResponse(res, "Logout successfully");
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};
