import { Request, Response } from "express";
import { getProfile, getProfiles } from "../utils/profile";
import Student from "../models/student";
import { createErrorResponse } from "../middlewares/errorHandler";
import { createOkResponse } from "../utils/misc";
import { AuthRequest } from "../middlewares/auth";
import Subscription from "../models/subscription.model";
import { Lesson } from "../models/Lesson";
import { Types } from "mongoose";
import {
  calculateLessonMetrics,
  formatLessonsForDashboard,
  getUserCalendars,
  calculateSubscriptionMetrics,
  getDateRange,
  getTodayRange
} from "../utils/dashboardHelpers";
import { createDefaultCalendar } from "../utils/calendarUtils";
import { hashPassword } from "../utils/hashing";

// Create a new learner under a tutor
export const createLearner = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { firstname, lastname, email, password, phoneno, assignedTutor } =
      req.body;

    if (!firstname || !lastname || !email || !password || !assignedTutor) {
      res.status(400).json({
        message:
          "Firstname, lastname, email, password, and assignedTutor are required.",
      });
      return;
    }

    // Check if assigned tutor exists and is a tutor
    const tutor = await getProfile({ _id: assignedTutor, role: "tutor" });
    if (!tutor) {
      res
        .status(404)
        .json({ message: "Assigned tutor not found or not a tutor." });
      return;
    }

    // Check if student already exists
    const existingStudent = await getProfile({ email });
    if (existingStudent) {
      res.status(400).json({ message: "Student with this email already exists." });
      return;
    }

    // Create new student
    const studentData = {
      firstname,
      lastname,
      email,
      password: await hashPassword(password),
      phoneno,
      role: "student",
      assignedTutor,
      ...req.body
    };

    const learner = await Student.create(studentData);

    // Create default calendar for the new student
    try {
      const userName = `${firstname} ${lastname}`.trim();

      await createDefaultCalendar({
        userId: learner._id,
        userType: 'student',
        userName: userName,
        timezone: learner.timezone || 'UTC'
      });

      console.log(`Created default calendar for new student: ${learner._id}`);
    } catch (calendarError) {
      console.error(`Failed to create default calendar for student ${learner._id}:`, calendarError);
      // Don't fail student creation if calendar creation fails
    }

    res.status(201).json({ success: true, data: learner });
  } catch (error: any) {
    console.error("Error creating learner:", error);
    res.status(500).json({ message: error.message || "Server Error" });
  }
};

// Get all learners (optionally filter by tutor)
export const getAllLearners = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { tutorId } = req.query;

    const query: any = { role: "student" };
    if (tutorId) {
      query.assignedTutor = tutorId;
    }

    const learners = await getProfiles(query);

    res.status(200).json(learners);
  } catch (error: any) {
    console.error("Error fetching learners:", error);
    res.status(500).json({ message: error.message || "Server Error" });
  }
};

// Delete a learner
export const deleteLearner = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    //   const { id } = req.params;
    //   const learner = await getProfileAndDelete({
    //     _id: id,
    //     role: "student",
    //   });
    //   if (!learner) {
    //     res.status(404).json({ message: "Learner not found." });
    //     return;
    //   }
    //   res.status(200).json({ message: "Learner deleted successfully." });
  } catch (error: any) {
    console.error("Error deleting learner:", error);
    res.status(500).json({ message: error.message || "Server Error" });
  }
};

// Get comprehensive student dashboard stats
export const getStudentDashboardStats = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can access dashboard stats', 403);
      return;
    }

    const studentId = req.user._id;

    // 1. Get active subscriptions
    const activeSubscriptions = await Subscription.find({
      studentId: studentId,
      status: 'active'
    }).populate('tutorId', 'firstname lastname email teachingSubjects rating profilePicture');

    // 2. Get all subscriptions for history
    const totalSubscriptions = await Subscription.countDocuments({
      studentId: studentId
    });

    // 3. Get lessons statistics
    const [totalLessons, completedLessons, upcomingLessons, cancelledLessons] = await Promise.all([
      Lesson.countDocuments({ studentId: studentId }),
      Lesson.countDocuments({ studentId: studentId, status: 'completed' }),
      Lesson.countDocuments({
        studentId: studentId,
        status: { $in: ['scheduled', 'confirmed'] },
        startDateTime: { $gte: new Date() }
      }),
      Lesson.countDocuments({ studentId: studentId, status: 'cancelled' })
    ]);

    // 4. Get total hours learned (sum of completed lesson durations)
    const totalHoursResult = await Lesson.aggregate([
      {
        $match: {
          studentId: new Types.ObjectId(studentId),
          status: 'completed'
        }
      },
      {
        $group: {
          _id: null,
          totalMinutes: { $sum: '$duration' }
        }
      }
    ]);

    const totalHoursLearned = totalHoursResult[0]?.totalMinutes
      ? Math.round((totalHoursResult[0].totalMinutes / 60) * 100) / 100
      : 0;

    // 5. Get trial status
    const student = await Student.findById(studentId);
    const trialStatus = {
      globalTrialUsed: student?.trialSettings?.globalTrialUsed || false,
      hasUsedFreeTrial: student?.hasUsedFreeTrial || false,
      availableTrials: student?.trialSettings?.tutorTrials?.size || 0
    };

    // 6. Get recent lessons (last 5)
    const recentLessons = await Lesson.find({
      studentId: studentId,
      status: 'completed'
    })
    .populate('tutorId', 'firstname lastname')
    .sort({ completedAt: -1 })
    .limit(5)
    .select('title subject startDateTime endDateTime duration tutorId lessonNotes');

    // 7. Calculate remaining lessons across all active subscriptions
    const totalRemainingLessons = activeSubscriptions.reduce((total, sub) => total + sub.remainingLessons, 0);

    createOkResponse(res, {
      data: {
        subscriptions: {
          active: activeSubscriptions.length,
          total: totalSubscriptions,
          activeSubscriptions: activeSubscriptions.map(sub => ({
            id: sub._id,
            tutorName: `${sub.tutorId.firstname} ${sub.tutorId.lastname}`,
            tutorEmail: sub.tutorId.email,
            tutorSubjects: sub.tutorId.teachingSubjects,
            tutorRating: sub.tutorId.rating,
            tutorProfilePicture: sub.tutorId.profilePicture,
            planType: sub.planType,
            lessonsPerWeek: sub.lessonsPerWeek,
            remainingLessons: sub.remainingLessons,
            currentPeriodEnd: sub.currentPeriodEnd,
            nextBillingDate: sub.nextBillingDate,
            monthlyPrice: sub.monthlyPrice,
            status: sub.status
          })),
          totalRemainingLessons
        },
        lessons: {
          total: totalLessons,
          completed: completedLessons,
          upcoming: upcomingLessons,
          cancelled: cancelledLessons,
          totalHoursLearned,
          recentLessons: recentLessons.map(lesson => ({
            id: lesson._id,
            title: lesson.title,
            subject: lesson.subject,
            tutorName: `${lesson.tutorId.firstname} ${lesson.tutorId.lastname}`,
            date: lesson.startDateTime,
            duration: lesson.duration,
            notes: lesson.lessonNotes
          }))
        },
        trialStatus,
        learningProgress: {
          totalHoursLearned,
          lessonsCompleted: completedLessons,
          averageLessonDuration: completedLessons > 0 ? Math.round((totalHoursResult[0]?.totalMinutes || 0) / completedLessons) : 0,
          activeTutors: activeSubscriptions.length
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching student dashboard stats:', error);
    createErrorResponse(res, error.message || 'Failed to fetch dashboard stats', 500);
  }
};

// Get upcoming lessons for student
export const getStudentUpcomingLessons = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can access their lessons', 403);
      return;
    }

    const studentId = req.user._id;
    const { limit = 10, startDate, endDate } = req.query;

    // Build date filter
    const dateFilter: any = {
      studentId: studentId,
      status: { $in: ['scheduled', 'confirmed'] },
      startDateTime: { $gte: new Date() }
    };

    if (startDate) {
      dateFilter.startDateTime.$gte = new Date(startDate as string);
    }
    if (endDate) {
      dateFilter.startDateTime.$lte = new Date(endDate as string);
    }

    const upcomingLessons = await Lesson.find(dateFilter)
      .populate('tutorId', 'firstname lastname email profilePicture teachingSubjects')
      .populate('subscriptionId', 'planType lessonsPerWeek')
      .sort({ startDateTime: 1 })
      .limit(parseInt(limit as string))
      .select('title subject description startDateTime endDateTime duration timezone lessonType isTrialLesson tutorId subscriptionId');

    const formattedLessons = upcomingLessons.map(lesson => ({
      id: lesson._id,
      title: lesson.title,
      subject: lesson.subject,
      description: lesson.description,
      startDateTime: lesson.startDateTime,
      endDateTime: lesson.endDateTime,
      duration: lesson.duration,
      timezone: lesson.timezone,
      lessonType: lesson.lessonType,
      isTrialLesson: lesson.isTrialLesson,
      tutor: {
        id: lesson.tutorId._id,
        name: `${lesson.tutorId.firstname} ${lesson.tutorId.lastname}`,
        email: lesson.tutorId.email,
        profilePicture: lesson.tutorId.profilePicture,
        subjects: lesson.tutorId.teachingSubjects
      },
      subscription: lesson.subscriptionId ? {
        planType: lesson.subscriptionId.planType,
        lessonsPerWeek: lesson.subscriptionId.lessonsPerWeek
      } : null
    }));

    createOkResponse(res, {
      data: {
        lessons: formattedLessons,
        total: formattedLessons.length,
        hasMore: formattedLessons.length === parseInt(limit as string)
      }
    });
  } catch (error: any) {
    console.error('Error fetching upcoming lessons:', error);
    createErrorResponse(res, error.message || 'Failed to fetch upcoming lessons', 500);
  }
};

// Get student's calendar view with lessons and availability
export const getStudentCalendarView = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can access their calendar', 403);
      return;
    }

    const studentId = req.user._id;
    const {
      startDate,
      endDate,
      viewType = 'month',
      includeTutorSchedules = false
    } = req.query;

    // Calculate date range
    const { startDate: defaultStart, endDate: defaultEnd } = getDateRange(30); // 30 days by default
    const start = startDate ? new Date(startDate as string) : defaultStart;
    const end = endDate ? new Date(endDate as string) : defaultEnd;

    // Get student's lessons in the date range
    const lessons = await Lesson.find({
      studentId: studentId,
      startDateTime: { $gte: start, $lte: end }
    })
    .populate('tutorId', 'firstname lastname email profilePicture teachingSubjects')
    .populate('subscriptionId', 'planType lessonsPerWeek')
    .sort({ startDateTime: 1 })
    .select('title subject description startDateTime endDateTime duration timezone status lessonType isTrialLesson tutorId subscriptionId');

    // Format lessons for display
    const formattedLessons = formatLessonsForDashboard(lessons, 'student');

    // Get student's calendars (if any)
    const studentCalendars = await getUserCalendars(studentId, 'student');

    // Get active subscriptions for context
    const subscriptionMetrics = await calculateSubscriptionMetrics(studentId);

    // Optionally include tutor schedules (if student has active subscriptions)
    let tutorSchedules: any[] = [];
    if (includeTutorSchedules === 'true' && subscriptionMetrics.activeSubscriptions.length > 0) {
      // This would require additional logic to fetch tutor availability
      // For now, we'll just indicate which tutors the student has subscriptions with
      tutorSchedules = subscriptionMetrics.activeSubscriptions.map(sub => ({
        tutorId: sub.id,
        tutorName: sub.tutorName,
        hasActiveSubscription: true
      }));
    }

    createOkResponse(res, {
      data: {
        dateRange: {
          start,
          end,
          viewType
        },
        lessons: formattedLessons,
        calendars: studentCalendars,
        subscriptions: subscriptionMetrics.activeSubscriptions,
        tutorSchedules,
        summary: {
          totalLessons: formattedLessons.length,
          upcomingLessons: formattedLessons.filter(l =>
            new Date(l.startDateTime) > new Date() &&
            ['scheduled', 'confirmed'].includes(l.status)
          ).length,
          completedLessons: formattedLessons.filter(l => l.status === 'completed').length,
          activeTutors: [...new Set(formattedLessons.map(l => l.tutor?.id))].filter(Boolean).length
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching student calendar view:', error);
    createErrorResponse(res, error.message || 'Failed to fetch calendar view', 500);
  }
};
