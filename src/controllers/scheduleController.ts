import { Response } from 'express';
import { Types } from 'mongoose';
import { Schedule, ISchedule, IDaySchedule, ITimeSlot, IScheduleException } from '../models/Schedule';
import { Calendar } from '../models/calendar';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

/**
 * Create a new schedule for a tutor
 */
export const createSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create schedules', 403);
      return;
    }

    const {
      calendarId,
      name,
      description,
      timezone = 'UTC',
      weeklySchedule,
      settings,
      isDefault = false
    } = req.body;

    // Validate required fields
    if (!calendarId || !name || !weeklySchedule) {
      createErrorResponse(res, 'calendarId, name, and weeklySchedule are required', 400);
      return;
    }

    if (!Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    // Verify calendar ownership
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.ownerId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this calendar', 403);
      return;
    }

    // Validate weekly schedule structure
    if (!Array.isArray(weeklySchedule) || weeklySchedule.length === 0) {
      createErrorResponse(res, 'weeklySchedule must be a non-empty array', 400);
      return;
    }

    // Create the schedule
    const schedule = new Schedule({
      tutorId: req.user._id,
      calendarId: new Types.ObjectId(calendarId),
      name,
      description,
      timezone,
      weeklySchedule,
      settings: {
        autoGenerateEvents: true,
        advanceGenerationDays: 30,
        defaultSessionDuration: 60,
        bufferTimeBetweenSessions: 15,
        allowBackToBackBookings: false,
        ...settings
      },
      isDefault,
      exceptions: []
    });

    await schedule.save();

    // If this is set as default, update the calendar
    if (isDefault) {
      await Calendar.findByIdAndUpdate(calendarId, {
        hasSchedule: true,
        defaultScheduleId: schedule._id
      });
    }

    const populatedSchedule = await Schedule.findById(schedule._id)
      .populate('calendarId', 'name color')
      .populate('tutorId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: 'Schedule created successfully',
      data: populatedSchedule
    });

  } catch (error) {
    console.error('Error creating schedule:', error);
    createErrorResponse(res, 'Failed to create schedule', 500);
  }
};

/**
 * Get tutor's schedules
 */
export const getTutorSchedules = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their schedules', 403);
      return;
    }

    const { calendarId, includeInactive = false } = req.query;

    // Build query
    const query: any = { tutorId: req.user._id };
    
    if (calendarId && Types.ObjectId.isValid(calendarId as string)) {
      query.calendarId = new Types.ObjectId(calendarId as string);
    }

    if (includeInactive !== 'true') {
      query.isActive = true;
    }

    const schedules = await Schedule.find(query)
      .populate('calendarId', 'name color isActive')
      .sort({ isDefault: -1, createdAt: -1 });

    // Add summary information for each schedule
    const schedulesWithSummary = schedules.map(schedule => {
      const workingDays = schedule.weeklySchedule.filter((day: IDaySchedule) => day.isWorkingDay).length;
      const totalSlots = schedule.weeklySchedule.reduce((total: number, day: IDaySchedule) => 
        total + day.timeSlots.filter((slot: ITimeSlot) => slot.isAvailable && slot.sessionType !== 'break').length, 0
      );

      return {
        ...schedule.toObject(),
        summary: {
          workingDaysPerWeek: workingDays,
          totalAvailableSlots: totalSlots,
          averageSlotsPerDay: workingDays > 0 ? Math.round(totalSlots / workingDays) : 0,
          upcomingExceptions: schedule.exceptions.filter((exc: IScheduleException) => 
            exc.date > new Date()
          ).length
        }
      };
    });

    res.json({
      success: true,
      data: schedulesWithSummary
    });

  } catch (error) {
    console.error('Error fetching tutor schedules:', error);
    createErrorResponse(res, 'Failed to fetch schedules', 500);
  }
};

/**
 * Get schedule by ID
 */
export const getScheduleById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view schedules', 403);
      return;
    }

    const { scheduleId } = req.params;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId)
      .populate('calendarId', 'name color timezone')
      .populate('tutorId', 'firstname lastname email');

    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId._id.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    res.json({
      success: true,
      data: schedule
    });

  } catch (error) {
    console.error('Error fetching schedule:', error);
    createErrorResponse(res, 'Failed to fetch schedule', 500);
  }
};

/**
 * Update schedule
 */
export const updateSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update schedules', 403);
      return;
    }

    const { scheduleId } = req.params;
    const updateData = req.body;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    // Update allowed fields
    const allowedFields = [
      'name', 'description', 'timezone', 'weeklySchedule', 
      'settings', 'isActive', 'isDefault', 'effectiveTo'
    ];

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        (schedule as any)[field] = updateData[field];
      }
    });

    await schedule.save();

    // If setting as default, update calendar
    if (updateData.isDefault === true) {
      await Calendar.findByIdAndUpdate(schedule.calendarId, {
        hasSchedule: true,
        defaultScheduleId: schedule._id
      });
    }

    const updatedSchedule = await Schedule.findById(schedule._id)
      .populate('calendarId', 'name color')
      .populate('tutorId', 'firstname lastname email');

    res.json({
      success: true,
      message: 'Schedule updated successfully',
      data: updatedSchedule
    });

  } catch (error) {
    console.error('Error updating schedule:', error);
    createErrorResponse(res, 'Failed to update schedule', 500);
  }
};

/**
 * Delete schedule
 */
export const deleteSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can delete schedules', 403);
      return;
    }

    const { scheduleId } = req.params;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    // If this is the default schedule, update the calendar
    if (schedule.isDefault) {
      await Calendar.findByIdAndUpdate(schedule.calendarId, {
        hasSchedule: false,
        defaultScheduleId: null
      });
    }

    await schedule.deleteOne();

    res.json({
      success: true,
      message: 'Schedule deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting schedule:', error);
    createErrorResponse(res, 'Failed to delete schedule', 500);
  }
};

/**
 * Add or update schedule exception (holiday, vacation, etc.)
 */
export const addScheduleException = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can manage schedule exceptions', 403);
      return;
    }

    const { scheduleId } = req.params;
    const { date, type, title, description, isRecurring = false, replacementSchedule } = req.body;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    if (!date || !type || !title) {
      createErrorResponse(res, 'date, type, and title are required', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    const exception: IScheduleException = {
      date: new Date(date),
      type,
      title,
      description,
      isRecurring,
      replacementSchedule
    };

    // Remove existing exception for the same date if any
    schedule.exceptions = schedule.exceptions.filter((exc: IScheduleException) =>
      exc.date.toDateString() !== exception.date.toDateString()
    );

    schedule.exceptions.push(exception);
    await schedule.save();

    res.json({
      success: true,
      message: 'Schedule exception added successfully',
      data: { exception }
    });

  } catch (error) {
    console.error('Error adding schedule exception:', error);
    createErrorResponse(res, 'Failed to add schedule exception', 500);
  }
};

/**
 * Remove schedule exception
 */
export const removeScheduleException = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can manage schedule exceptions', 403);
      return;
    }

    const { scheduleId } = req.params;
    const { date } = req.body;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    if (!date) {
      createErrorResponse(res, 'date is required', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    const exceptionDate = new Date(date);
    const initialLength = schedule.exceptions.length;

    schedule.exceptions = schedule.exceptions.filter((exc: IScheduleException) =>
      exc.date.toDateString() !== exceptionDate.toDateString()
    );

    if (schedule.exceptions.length === initialLength) {
      createErrorResponse(res, 'No exception found for the specified date', 404);
      return;
    }

    await schedule.save();

    res.json({
      success: true,
      message: 'Schedule exception removed successfully'
    });

  } catch (error) {
    console.error('Error removing schedule exception:', error);
    createErrorResponse(res, 'Failed to remove schedule exception', 500);
  }
};

/**
 * Get schedule availability for a date range
 */
export const getScheduleAvailability = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { scheduleId } = req.params;
    const { startDate, endDate, includeBooked = false } = req.query;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId)
      .populate('calendarId', 'name color');

    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // For non-tutors, only show public schedules
    if (!req.user || req.user.role !== 'tutor' ||
        (req.user.role === 'tutor' && schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString())) {
      const calendar = await Calendar.findById(schedule.calendarId);
      if (!calendar || !calendar.isShared || !calendar.isActive) {
        createErrorResponse(res, 'Schedule not accessible', 403);
        return;
      }
    }

    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    const availability = [];
    const currentDate = new Date(start);

    while (currentDate <= end) {
      const daySchedule = schedule.getScheduleForDate(currentDate);

      if (daySchedule && daySchedule.isWorkingDay) {
        const availableSlots = schedule.getAvailableSlots(currentDate);

        // If requested, check for existing bookings (using Lesson model instead of Event)
        let slotsWithBookingInfo = availableSlots;
        if (includeBooked === 'true') {
          // Import Lesson model at the top if not already imported
          const { Lesson } = await import('../models/Lesson');

          const dayLessons = await Lesson.find({
            tutorCalendarId: schedule.calendarId,
            startDateTime: {
              $gte: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()),
              $lt: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + 1)
            },
            status: { $in: ['scheduled', 'confirmed', 'in_progress'] }
          });

          slotsWithBookingInfo = availableSlots.map((slot: ITimeSlot) => ({
            ...slot,
            isBooked: dayLessons.some((lesson: any) => {
              const lessonTime = lesson.startDateTime.toTimeString().substring(0, 5);
              return lessonTime >= slot.startTime && lessonTime < slot.endTime;
            })
          }));
        }

        availability.push({
          date: new Date(currentDate),
          dayOfWeek: currentDate.getDay(),
          isWorkingDay: true,
          slots: slotsWithBookingInfo,
          breakTimes: daySchedule.breakTimes || [],
          dayNotes: daySchedule.dayNotes
        });
      } else {
        availability.push({
          date: new Date(currentDate),
          dayOfWeek: currentDate.getDay(),
          isWorkingDay: false,
          slots: [],
          reason: daySchedule ? 'No working day' : 'Schedule exception'
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    res.json({
      success: true,
      data: {
        schedule: {
          _id: schedule._id,
          name: schedule.name,
          timezone: schedule.timezone,
          calendar: schedule.calendarId
        },
        dateRange: { startDate: start, endDate: end },
        availability
      }
    });

  } catch (error) {
    console.error('Error getting schedule availability:', error);
    createErrorResponse(res, 'Failed to get schedule availability', 500);
  }
};
