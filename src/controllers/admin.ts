import { Request, Response } from "express";
import { Types } from "mongoose";
import Tutor from "../models/tutor";
import Student from "../models/student";
import Admin from "../models/admin";
import Subscription from "../models/subscription.model";
import { Lesson } from "../models/Lesson";
import TransactionModel from "../models/transaction.model";
import EscrowModel from "../models/escrow.model";
import { AuthRequest } from "../types/AuthRequest";
import { createErrorResponse } from "../middlewares/errorHandler";

// ============================================================================
// PERMISSION MIDDLEWARE HELPER
// ============================================================================

// Note: Permission checking is handled by the requireAdmin middleware in routes
// Individual permission checks can be added here if needed in the future

// ============================================================================
// DASHBOARD & ANALYTICS
// ============================================================================

export const getDashboardStats = async (_req: AuthRequest, res: Response): Promise<void> => {
  try {
    const [
      totalUsers,
      totalTutors,
      totalStudents,
      pendingTutors,
      activeTutors,
      totalSubscriptions,
      activeSubscriptions,
      totalLessons,
      completedLessons,
      flaggedContent,
      revenueThisMonth
    ] = await Promise.all([
      // User counts
      Promise.all([
        Tutor.countDocuments(),
        Student.countDocuments()
      ]).then(([tutors, students]) => tutors + students),

      Tutor.countDocuments(),
      Student.countDocuments(),
      Tutor.countDocuments({ approvalStatus: "pending" }),
      Tutor.countDocuments({ approvalStatus: "approved", isActive: true }),

      // Subscription stats
      Subscription.countDocuments(),
      Subscription.countDocuments({ status: "active" }),

      // Lesson stats
      Lesson.countDocuments(),
      Lesson.countDocuments({ status: "completed" }),

      // Flagged content
      Tutor.countDocuments({ isFlagged: true }),

      // Revenue calculation (simplified)
      Subscription.aggregate([
        {
          $match: {
            status: "active",
            createdAt: {
              $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: "$monthlyPrice" }
          }
        }
      ]).then(result => result[0]?.total || 0)
    ]);

    // Recent activity
    const recentTutors = await Tutor.find({ approvalStatus: "pending" })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('firstname lastname email createdAt');

    const recentSubscriptions = await Subscription.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('studentId', 'firstname lastname')
      .populate('tutorId', 'firstname lastname')
      .select('planType monthlyPrice status createdAt');

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          totalTutors,
          totalStudents,
          pendingTutors,
          activeTutors,
          totalSubscriptions,
          activeSubscriptions,
          totalLessons,
          completedLessons,
          flaggedContent,
          revenueThisMonth
        },
        recentActivity: {
          newTutorApplications: recentTutors,
          newSubscriptions: recentSubscriptions
        }
      }
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    createErrorResponse(res, "Failed to fetch dashboard statistics", 500);
  }
};

export const getAnalytics = async (_req: Request, res: Response): Promise<void> => {
  try {
    const totalTutors = await Tutor.countDocuments();
    const approvedTutors = await Tutor.countDocuments({ approvalStatus: "approved" });
    const flaggedTutors = await Tutor.countDocuments({ isFlagged: true });

    res.json({
      totalTutors,
      approvedTutors,
      flaggedTutors,
      reviewFlaggedCount: await Tutor.aggregate([
        { $unwind: "$reviews" },
        { $match: { "reviews.flagged": true } },
        { $count: "count" },
      ]).then((r) => r[0]?.count || 0),
    });
  } catch (error) {
    console.error("Error fetching analytics:", error);
    createErrorResponse(res, "Analytics service down. Try again later.", 500);
  }
};

// ============================================================================
// TUTOR MANAGEMENT
// ============================================================================

export const approveTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      {
        approvalStatus: "approved",
        isActive: true
      },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Tutor approved successfully",
      data: tutor
    });
  } catch (error) {
    console.error("Error approving tutor:", error);
    createErrorResponse(res, "Failed to approve tutor", 500);
  }
};

export const rejectTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;
  const { rejectionReason } = req.body;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    if (!rejectionReason) {
      createErrorResponse(res, "Rejection reason is required", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      {
        approvalStatus: "rejected",
        isActive: false
      },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Tutor rejected",
      data: tutor
    });
  } catch (error) {
    console.error("Error rejecting tutor:", error);
    createErrorResponse(res, "Failed to reject tutor", 500);
  }
};

export const getPendingTutors = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [tutors, total] = await Promise.all([
      Tutor.find({ approvalStatus: "pending" })
        .select('firstname lastname email teachingSubjects basePrice createdAt approvalStatus')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      Tutor.countDocuments({ approvalStatus: "pending" })
    ]);

    res.json({
      success: true,
      data: tutors,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching pending tutors:", error);
    createErrorResponse(res, "Failed to fetch pending tutors", 500);
  }
};

export const getAllTutors = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      approvalStatus,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (status) query.isActive = status === 'active';
    if (approvalStatus) query.approvalStatus = approvalStatus;
    if (search) {
      query.$or = [
        { firstname: { $regex: search, $options: 'i' } },
        { lastname: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [tutors, total] = await Promise.all([
      Tutor.find(query)
        .select('firstname lastname email teachingSubjects basePrice rating totalLessons approvalStatus isActive isFlagged createdAt')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      Tutor.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: tutors,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching tutors:", error);
    createErrorResponse(res, "Failed to fetch tutors", 500);
  }
};

export const getTutorById = async (req: Request, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findById(tutorId)
      .populate('subscriptions')
      .populate('lessons');

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      data: tutor
    });
  } catch (error) {
    console.error("Error fetching tutor:", error);
    createErrorResponse(res, "Failed to fetch tutor", 500);
  }
};

export const flagTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      { isFlagged: true },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Tutor flagged successfully",
      data: tutor
    });
  } catch (error) {
    console.error("Error flagging tutor:", error);
    createErrorResponse(res, "Failed to flag tutor", 500);
  }
};

export const unflagTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      { isFlagged: false },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Tutor unflagged successfully",
      data: tutor
    });
  } catch (error) {
    console.error("Error unflagging tutor:", error);
    createErrorResponse(res, "Failed to unflag tutor", 500);
  }
};

export const suspendTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;
  const { reason, duration } = req.body;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    if (!reason) {
      createErrorResponse(res, "Suspension reason is required", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      { isActive: false },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: `Tutor suspended ${duration ? `for ${duration} days` : 'indefinitely'}`,
      data: tutor
    });
  } catch (error) {
    console.error("Error suspending tutor:", error);
    createErrorResponse(res, "Failed to suspend tutor", 500);
  }
};

// ============================================================================
// STUDENT MANAGEMENT
// ============================================================================

export const getAllStudents = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (status) query.isActive = status === 'active';
    if (search) {
      query.$or = [
        { firstname: { $regex: search, $options: 'i' } },
        { lastname: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [students, total] = await Promise.all([
      Student.find(query)
        .select('firstname lastname email learningReasons skillsToImprove isActive hasUsedFreeTrial createdAt')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      Student.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: students,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching students:", error);
    createErrorResponse(res, "Failed to fetch students", 500);
  }
};

export const getStudentById = async (req: Request, res: Response): Promise<void> => {
  const { studentId } = req.params;

  try {
    if (!Types.ObjectId.isValid(studentId)) {
      createErrorResponse(res, "Invalid student ID format", 400);
      return;
    }

    const student = await Student.findById(studentId)
      .populate('subscriptions')
      .populate('lessons');

    if (!student) {
      createErrorResponse(res, "Student not found", 404);
      return;
    }

    res.json({
      success: true,
      data: student
    });
  } catch (error) {
    console.error("Error fetching student:", error);
    createErrorResponse(res, "Failed to fetch student", 500);
  }
};

export const suspendStudent = async (req: AuthRequest, res: Response): Promise<void> => {
  const { studentId } = req.params;
  const { reason, duration } = req.body;

  try {
    if (!Types.ObjectId.isValid(studentId)) {
      createErrorResponse(res, "Invalid student ID format", 400);
      return;
    }

    if (!reason) {
      createErrorResponse(res, "Suspension reason is required", 400);
      return;
    }

    const student = await Student.findByIdAndUpdate(
      studentId,
      { isActive: false },
      { new: true }
    );

    if (!student) {
      createErrorResponse(res, "Student not found", 404);
      return;
    }

    res.json({
      success: true,
      message: `Student suspended ${duration ? `for ${duration} days` : 'indefinitely'}`,
      data: student
    });
  } catch (error) {
    console.error("Error suspending student:", error);
    createErrorResponse(res, "Failed to suspend student", 500);
  }
};

// ============================================================================
// SUBSCRIPTION MANAGEMENT
// ============================================================================

export const getAllSubscriptions = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (status) query.status = status;

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [subscriptions, total] = await Promise.all([
      Subscription.find(query)
        .populate('studentId', 'firstname lastname email')
        .populate('tutorId', 'firstname lastname email')
        .select('planType lessonsPerWeek monthlyPrice status currentPeriodStart currentPeriodEnd createdAt')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      Subscription.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: subscriptions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    createErrorResponse(res, "Failed to fetch subscriptions", 500);
  }
};

export const getSubscriptionById = async (req: Request, res: Response): Promise<void> => {
  const { subscriptionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(subscriptionId)) {
      createErrorResponse(res, "Invalid subscription ID format", 400);
      return;
    }

    const subscription = await Subscription.findById(subscriptionId)
      .populate('studentId')
      .populate('tutorId');

    if (!subscription) {
      createErrorResponse(res, "Subscription not found", 404);
      return;
    }

    res.json({
      success: true,
      data: subscription
    });
  } catch (error) {
    console.error("Error fetching subscription:", error);
    createErrorResponse(res, "Failed to fetch subscription", 500);
  }
};

export const cancelSubscription = async (req: AuthRequest, res: Response): Promise<void> => {
  const { subscriptionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(subscriptionId)) {
      createErrorResponse(res, "Invalid subscription ID format", 400);
      return;
    }

    const subscription = await Subscription.findByIdAndUpdate(
      subscriptionId,
      {
        status: 'cancelled',
        autoRenew: false
      },
      { new: true }
    );

    if (!subscription) {
      createErrorResponse(res, "Subscription not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Subscription cancelled successfully",
      data: subscription
    });
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    createErrorResponse(res, "Failed to cancel subscription", 500);
  }
};

// ============================================================================
// CONTENT MODERATION
// ============================================================================

export const getFlaggedItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const [flaggedTutors, flaggedReviews, total] = await Promise.all([
      Tutor.find({ isFlagged: true })
        .select('firstname lastname email isFlagged createdAt')
        .skip(skip)
        .limit(Number(limit)),

      Tutor.find({ "reviews.flagged": true })
        .select('firstname lastname email reviews')
        .skip(skip)
        .limit(Number(limit)),

      Promise.all([
        Tutor.countDocuments({ isFlagged: true }),
        Tutor.countDocuments({ "reviews.flagged": true })
      ]).then(([tutors, reviews]) => tutors + reviews)
    ]);

    res.json({
      success: true,
      data: {
        flaggedTutors,
        flaggedReviews: flaggedReviews.map(tutor => ({
          ...tutor.toObject(),
          reviews: tutor.reviews.filter(review => review.flagged)
        }))
      },
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching flagged items:", error);
    createErrorResponse(res, "Failed to fetch flagged items", 500);
  }
};

export const moderateContent = async (req: AuthRequest, res: Response): Promise<void> => {
  const { contentType, contentId, action } = req.body;

  try {
    if (!['approve', 'remove', 'flag'].includes(action)) {
      createErrorResponse(res, "Invalid action. Must be 'approve', 'remove', or 'flag'", 400);
      return;
    }

    let result;

    switch (contentType) {
      case 'tutor':
        if (action === 'flag') {
          result = await Tutor.findByIdAndUpdate(contentId, { isFlagged: true }, { new: true });
        } else if (action === 'approve') {
          result = await Tutor.findByIdAndUpdate(contentId, { isFlagged: false }, { new: true });
        }
        break;

      case 'review':
        // Handle review moderation
        const tutor = await Tutor.findOne({ "reviews._id": contentId });
        if (tutor) {
          const review = tutor.reviews.find((r: any) => r._id.toString() === contentId);
          if (review) {
            review.flagged = action === 'flag';
            result = await tutor.save();
          }
        }
        break;

      default:
        createErrorResponse(res, "Invalid content type", 400);
        return;
    }

    if (!result) {
      createErrorResponse(res, "Content not found", 404);
      return;
    }

    res.json({
      success: true,
      message: `Content ${action}ed successfully`,
      data: result
    });
  } catch (error) {
    console.error("Error moderating content:", error);
    createErrorResponse(res, "Failed to moderate content", 500);
  }
};

// ============================================================================
// ADMIN MANAGEMENT
// ============================================================================

export const getAllAdmins = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20, level, department } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    if (level) query.adminLevel = Number(level);
    if (department) query.department = department;

    const [admins, total] = await Promise.all([
      Admin.find(query)
        .select('firstname lastname email adminLevel department permissions isActive lastActiveAt createdAt')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Admin.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: admins,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching admins:", error);
    createErrorResponse(res, "Failed to fetch admins", 500);
  }
};

export const createAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  const {
    firstname,
    lastname,
    email,
    password,
    adminLevel,
    department,
    permissions
  } = req.body;

  try {
    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email });
    if (existingAdmin) {
      createErrorResponse(res, "Admin with this email already exists", 409);
      return;
    }

    const newAdmin = new Admin({
      firstname,
      lastname,
      email,
      password, // Should be hashed in pre-save middleware
      role: 'admin',
      adminLevel: adminLevel || 1,
      department,
      permissions: permissions || [],
      createdBy: req.user?._id,
      isActive: true
    });

    await newAdmin.save();

    // Remove password from response
    const adminResponse = newAdmin.toObject();
    delete adminResponse.password;

    res.status(201).json({
      success: true,
      message: "Admin created successfully",
      data: adminResponse
    });
  } catch (error) {
    console.error("Error creating admin:", error);
    createErrorResponse(res, "Failed to create admin", 500);
  }
};

export const updateAdminPermissions = async (req: AuthRequest, res: Response): Promise<void> => {
  const { adminId } = req.params;
  const { permissions, adminLevel } = req.body;

  try {
    if (!Types.ObjectId.isValid(adminId)) {
      createErrorResponse(res, "Invalid admin ID format", 400);
      return;
    }

    // Validate required fields
    if (!permissions && adminLevel === undefined) {
      createErrorResponse(res, "At least one field (permissions or adminLevel) is required", 400);
      return;
    }

    // Validate permissions array if provided
    if (permissions && !Array.isArray(permissions)) {
      createErrorResponse(res, "Permissions must be an array", 400);
      return;
    }

    // Validate adminLevel if provided
    if (adminLevel !== undefined && (typeof adminLevel !== 'number' || adminLevel < 1 || adminLevel > 5)) {
      createErrorResponse(res, "Admin level must be a number between 1 and 5", 400);
      return;
    }

    const admin = await Admin.findByIdAndUpdate(
      adminId,
      {
        permissions,
        adminLevel,
        modifiedBy: req.user?._id,
        modifiedAt: new Date()
      },
      { new: true }
    ).select('-password');

    if (!admin) {
      createErrorResponse(res, "Admin not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Admin permissions updated successfully",
      data: admin
    });
  } catch (error) {
    console.error("Error updating admin permissions:", error);
    createErrorResponse(res, "Failed to update admin permissions", 500);
  }
};

// ============================================================================
// FINANCIAL MANAGEMENT & PAYMENT TRACKING
// ============================================================================

export const getFinancialOverview = async (_req: AuthRequest, res: Response): Promise<void> => {
  try {
    const [
      totalRevenue,
      monthlyRevenue,
      totalTransactions,
      pendingPayouts,
      escrowBalance,
      refundedAmount,
      platformFees,
      activeSubscriptions,
      failedPayments
    ] = await Promise.all([
      // Total revenue from all successful transactions
      TransactionModel.aggregate([
        { $match: { status: 'completed', type: { $in: ['subscription_payment', 'fee'] } } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Monthly revenue (current month)
      TransactionModel.aggregate([
        {
          $match: {
            status: 'completed',
            type: { $in: ['subscription_payment', 'fee'] },
            createdAt: {
              $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Total transaction count
      TransactionModel.countDocuments(),

      // Pending tutor payouts
      TransactionModel.aggregate([
        { $match: { status: 'pending', type: 'lesson_payout' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Total amount in escrow
      EscrowModel.aggregate([
        { $match: { status: 'held' } },
        { $group: { _id: null, total: { $sum: '$amountHeld' } } }
      ]).then(result => result[0]?.total || 0),

      // Total refunded amount
      TransactionModel.aggregate([
        { $match: { status: 'completed', type: 'refund' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Platform fees collected
      EscrowModel.aggregate([
        { $match: { status: { $in: ['released', 'held'] } } },
        { $group: { _id: null, total: { $sum: '$platformFee' } } }
      ]).then(result => result[0]?.total || 0),

      // Active subscriptions count
      Subscription.countDocuments({ status: 'active' }),

      // Failed payments count (last 30 days)
      TransactionModel.countDocuments({
        status: 'failed',
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      })
    ]);

    // Recent transactions
    const recentTransactions = await TransactionModel.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('userId', 'firstname lastname email')
      .select('amount type status description createdAt');

    // Payment method breakdown
    const paymentMethodStats = await Subscription.aggregate([
      { $match: { status: 'active' } },
      {
        $group: {
          _id: '$planType',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$monthlyPrice' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalRevenue: totalRevenue / 100, // Convert from cents
          monthlyRevenue: monthlyRevenue / 100,
          totalTransactions,
          pendingPayouts: pendingPayouts / 100,
          escrowBalance: escrowBalance / 100,
          refundedAmount: refundedAmount / 100,
          platformFees: platformFees / 100,
          activeSubscriptions,
          failedPayments
        },
        recentTransactions,
        paymentMethodStats
      }
    });
  } catch (error) {
    console.error("Error fetching financial overview:", error);
    createErrorResponse(res, "Failed to fetch financial overview", 500);
  }
};

export const getAllTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      userId,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (type) query.type = type;
    if (status) query.status = status;
    if (userId && Types.ObjectId.isValid(userId as string)) {
      query.userId = new Types.ObjectId(userId as string);
    }
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [transactions, total, summary] = await Promise.all([
      TransactionModel.find(query)
        .populate('userId', 'firstname lastname email role')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      TransactionModel.countDocuments(query),
      TransactionModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' },
            completedAmount: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, '$amount', 0] }
            },
            pendingAmount: {
              $sum: { $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0] }
            },
            failedAmount: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, '$amount', 0] }
            }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: transactions,
      summary: summary[0] ? {
        totalAmount: summary[0].totalAmount / 100,
        completedAmount: summary[0].completedAmount / 100,
        pendingAmount: summary[0].pendingAmount / 100,
        failedAmount: summary[0].failedAmount / 100
      } : null,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    createErrorResponse(res, "Failed to fetch transactions", 500);
  }
};

export const getTransactionById = async (req: Request, res: Response): Promise<void> => {
  const { transactionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(transactionId)) {
      createErrorResponse(res, "Invalid transaction ID format", 400);
      return;
    }

    const transaction = await TransactionModel.findById(transactionId)
      .populate('userId', 'firstname lastname email role');

    if (!transaction) {
      createErrorResponse(res, "Transaction not found", 404);
      return;
    }

    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error("Error fetching transaction:", error);
    createErrorResponse(res, "Failed to fetch transaction", 500);
  }
};

export const getAllEscrowTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      tutorId,
      studentId,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (status) query.status = status;
    if (tutorId && Types.ObjectId.isValid(tutorId as string)) {
      query.tutorId = new Types.ObjectId(tutorId as string);
    }
    if (studentId && Types.ObjectId.isValid(studentId as string)) {
      query.studentId = new Types.ObjectId(studentId as string);
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [escrowTransactions, total, summary] = await Promise.all([
      EscrowModel.find(query)
        .populate('tutorId', 'firstname lastname email')
        .populate('studentId', 'firstname lastname email')
        .populate('lessonId', 'title scheduledAt')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      EscrowModel.countDocuments(query),
      EscrowModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalHeld: { $sum: '$amountHeld' },
            totalFees: { $sum: '$platformFee' },
            totalPayouts: { $sum: '$tutorPayout' },
            heldCount: { $sum: { $cond: [{ $eq: ['$status', 'held'] }, 1, 0] } },
            releasedCount: { $sum: { $cond: [{ $eq: ['$status', 'released'] }, 1, 0] } },
            refundedCount: { $sum: { $cond: [{ $eq: ['$status', 'refunded'] }, 1, 0] } }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: escrowTransactions,
      summary: summary[0] ? {
        totalHeld: summary[0].totalHeld / 100,
        totalFees: summary[0].totalFees / 100,
        totalPayouts: summary[0].totalPayouts / 100,
        heldCount: summary[0].heldCount,
        releasedCount: summary[0].releasedCount,
        refundedCount: summary[0].refundedCount
      } : null,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching escrow transactions:", error);
    createErrorResponse(res, "Failed to fetch escrow transactions", 500);
  }
};

export const releaseEscrowFunds = async (req: AuthRequest, res: Response): Promise<void> => {
  const { escrowId } = req.params;
  const { reason } = req.body;

  try {
    if (!Types.ObjectId.isValid(escrowId)) {
      createErrorResponse(res, "Invalid escrow ID format", 400);
      return;
    }

    const escrow = await EscrowModel.findById(escrowId)
      .populate('tutorId', 'firstname lastname email')
      .populate('lessonId', 'title');

    if (!escrow) {
      createErrorResponse(res, "Escrow transaction not found", 404);
      return;
    }

    if (escrow.status !== 'held') {
      createErrorResponse(res, "Escrow funds can only be released from 'held' status", 400);
      return;
    }

    // Update escrow status
    escrow.status = 'released';
    escrow.releasedAt = new Date();
    await escrow.save();

    // Create payout transaction for tutor
    const payoutTransaction = new TransactionModel({
      userId: escrow.tutorId,
      amount: escrow.tutorPayout,
      type: 'lesson_payout',
      status: 'completed',
      description: `Payout for lesson: ${(escrow as any).lessonId?.title || 'Unknown'} - ${reason || 'Admin release'}`
    });
    await payoutTransaction.save();

    res.json({
      success: true,
      message: "Escrow funds released successfully",
      data: {
        escrow,
        payoutTransaction
      }
    });
  } catch (error) {
    console.error("Error releasing escrow funds:", error);
    createErrorResponse(res, "Failed to release escrow funds", 500);
  }
};

export const processRefund = async (req: AuthRequest, res: Response): Promise<void> => {
  const { subscriptionId } = req.params;
  const { amount, reason, refundType = 'partial' } = req.body;

  try {
    if (!Types.ObjectId.isValid(subscriptionId)) {
      createErrorResponse(res, "Invalid subscription ID format", 400);
      return;
    }

    if (!amount || amount <= 0) {
      createErrorResponse(res, "Valid refund amount is required", 400);
      return;
    }

    if (!reason) {
      createErrorResponse(res, "Refund reason is required", 400);
      return;
    }

    const subscription = await Subscription.findById(subscriptionId)
      .populate('studentId', 'firstname lastname email');

    if (!subscription) {
      createErrorResponse(res, "Subscription not found", 404);
      return;
    }

    // Create refund transaction
    const refundTransaction = new TransactionModel({
      userId: subscription.studentId,
      amount: Math.round(amount * 100), // Convert to cents
      type: 'refund',
      status: 'completed',
      description: `${refundType} refund for subscription ${subscription.planType} - ${reason}`
    });
    await refundTransaction.save();

    // Update subscription status if full refund
    if (refundType === 'full') {
      subscription.status = 'cancelled';
      await subscription.save();
    }

    res.json({
      success: true,
      message: "Refund processed successfully",
      data: {
        refundTransaction,
        subscription: refundType === 'full' ? subscription : null
      }
    });
  } catch (error) {
    console.error("Error processing refund:", error);
    createErrorResponse(res, "Failed to process refund", 500);
  }
};

export const getFinancialReports = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      reportType = 'monthly',
      startDate,
      endDate,
      year = new Date().getFullYear()
    } = req.query;

    let dateFilter: any = {};

    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate as string),
          $lte: new Date(endDate as string)
        }
      };
    } else if (reportType === 'monthly') {
      // Current year monthly breakdown
      dateFilter = {
        createdAt: {
          $gte: new Date(Number(year), 0, 1),
          $lt: new Date(Number(year) + 1, 0, 1)
        }
      };
    }

    const [
      revenueByMonth,
      transactionsByType,
      subscriptionMetrics,
      tutorPayouts,
      refundAnalysis
    ] = await Promise.all([
      // Monthly revenue breakdown
      TransactionModel.aggregate([
        {
          $match: {
            ...dateFilter,
            status: 'completed',
            type: { $in: ['subscription_payment', 'fee'] }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            revenue: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]),

      // Transaction breakdown by type
      TransactionModel.aggregate([
        { $match: { ...dateFilter, status: 'completed' } },
        {
          $group: {
            _id: '$type',
            total: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Subscription metrics
      Subscription.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalRevenue: { $sum: '$monthlyPrice' }
          }
        }
      ]),

      // Tutor payout summary
      TransactionModel.aggregate([
        {
          $match: {
            ...dateFilter,
            type: 'lesson_payout',
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            totalPayouts: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Refund analysis
      TransactionModel.aggregate([
        {
          $match: {
            ...dateFilter,
            type: 'refund',
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            totalRefunds: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: {
        reportType,
        period: { startDate, endDate, year },
        revenueByMonth: revenueByMonth.map(item => ({
          ...item,
          revenue: item.revenue / 100
        })),
        transactionsByType: transactionsByType.map(item => ({
          ...item,
          total: item.total / 100
        })),
        subscriptionMetrics,
        tutorPayouts: tutorPayouts[0] ? {
          totalPayouts: tutorPayouts[0].totalPayouts / 100,
          count: tutorPayouts[0].count
        } : { totalPayouts: 0, count: 0 },
        refundAnalysis: refundAnalysis[0] ? {
          totalRefunds: refundAnalysis[0].totalRefunds / 100,
          count: refundAnalysis[0].count
        } : { totalRefunds: 0, count: 0 }
      }
    });
  } catch (error) {
    console.error("Error generating financial reports:", error);
    createErrorResponse(res, "Failed to generate financial reports", 500);
  }
};

export const getPaymentMethodAnalytics = async (_req: AuthRequest, res: Response): Promise<void> => {
  try {
    const [
      subscriptionsByPlan,
      paymentFailureRate,
      averageSubscriptionValue,
      churnAnalysis
    ] = await Promise.all([
      // Subscription breakdown by plan type
      Subscription.aggregate([
        { $match: { status: 'active' } },
        {
          $group: {
            _id: '$planType',
            count: { $sum: 1 },
            totalRevenue: { $sum: '$monthlyPrice' },
            avgPrice: { $avg: '$monthlyPrice' }
          }
        },
        { $sort: { count: -1 } }
      ]),

      // Payment failure rate (last 30 days)
      TransactionModel.aggregate([
        {
          $match: {
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
            type: 'subscription_payment'
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]),

      // Average subscription value
      Subscription.aggregate([
        { $match: { status: 'active' } },
        {
          $group: {
            _id: null,
            avgMonthlyValue: { $avg: '$monthlyPrice' },
            totalActiveSubscriptions: { $sum: 1 }
          }
        }
      ]),

      // Churn analysis (cancelled subscriptions in last 30 days)
      Subscription.aggregate([
        {
          $match: {
            status: 'cancelled',
            updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: "%Y-%m-%d", date: "$updatedAt" }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    // Calculate failure rate percentage
    const totalPaymentAttempts = paymentFailureRate.reduce((sum, item) => sum + item.count, 0);
    const failedPayments = paymentFailureRate.find(item => item._id === 'failed')?.count || 0;
    const failureRatePercentage = totalPaymentAttempts > 0 ? (failedPayments / totalPaymentAttempts) * 100 : 0;

    res.json({
      success: true,
      data: {
        subscriptionsByPlan: subscriptionsByPlan.map(plan => ({
          ...plan,
          totalRevenue: plan.totalRevenue / 100,
          avgPrice: plan.avgPrice / 100
        })),
        paymentMetrics: {
          failureRate: Math.round(failureRatePercentage * 100) / 100,
          totalAttempts: totalPaymentAttempts,
          failedPayments,
          successfulPayments: totalPaymentAttempts - failedPayments
        },
        averageSubscriptionValue: averageSubscriptionValue[0] ? {
          avgMonthlyValue: averageSubscriptionValue[0].avgMonthlyValue / 100,
          totalActiveSubscriptions: averageSubscriptionValue[0].totalActiveSubscriptions
        } : null,
        churnAnalysis
      }
    });
  } catch (error) {
    console.error("Error fetching payment method analytics:", error);
    createErrorResponse(res, "Failed to fetch payment method analytics", 500);
  }
};

export const getTutorPayoutSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      tutorId,
      status = 'all',
      startDate,
      endDate
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    let query: any = { type: 'lesson_payout' };

    // Filters
    if (tutorId && Types.ObjectId.isValid(tutorId as string)) {
      query.userId = new Types.ObjectId(tutorId as string);
    }
    if (status !== 'all') {
      query.status = status;
    }
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const [payouts, total, summary] = await Promise.all([
      TransactionModel.find(query)
        .populate('userId', 'firstname lastname email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      TransactionModel.countDocuments(query),
      TransactionModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$status',
            total: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    // Get top earning tutors
    const topEarners = await TransactionModel.aggregate([
      {
        $match: {
          type: 'lesson_payout',
          status: 'completed',
          createdAt: startDate || endDate ? {
            ...(startDate && { $gte: new Date(startDate as string) }),
            ...(endDate && { $lte: new Date(endDate as string) })
          } : { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: '$userId',
          totalEarnings: { $sum: '$amount' },
          payoutCount: { $sum: 1 }
        }
      },
      { $sort: { totalEarnings: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'tutors',
          localField: '_id',
          foreignField: '_id',
          as: 'tutor'
        }
      },
      { $unwind: '$tutor' }
    ]);

    res.json({
      success: true,
      data: payouts,
      summary: summary.map(item => ({
        status: item._id,
        total: item.total / 100,
        count: item.count
      })),
      topEarners: topEarners.map(earner => ({
        tutor: {
          _id: earner._id,
          firstname: earner.tutor.firstname,
          lastname: earner.tutor.lastname,
          email: earner.tutor.email
        },
        totalEarnings: earner.totalEarnings / 100,
        payoutCount: earner.payoutCount
      })),
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching tutor payout summary:", error);
    createErrorResponse(res, "Failed to fetch tutor payout summary", 500);
  }
};
