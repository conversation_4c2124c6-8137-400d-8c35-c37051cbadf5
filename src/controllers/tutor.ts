import { Request, Response } from "express";
import { getProfile, getProfileModelByRole } from "../utils/profile";
import { createErrorResponse } from "../middlewares/errorHandler";
import { createOkResponse } from "../utils/misc";
import { endOfPeriod, startOfPeriod } from "../utils/datetime";
import TransactionModel from "../models/transaction.model";
import Student from "../models/student";
import Subscription from "../models/subscription.model";
import EscrowModel from "../models/escrow.model";
import { hashPassword } from "../utils/hashing";
import { AuthRequest } from "../middlewares/auth";
import { Lesson } from "../models/Lesson";
import { Calendar } from "../models/calendar";
import { Schedule } from "../models/Schedule";
import { Types } from "mongoose";
import {
  calculateLessonMetrics,
  formatLessonsForDashboard,
  getUserCalendars,
  getTutorSchedules,
  getDateRange,
  getTodayRange,
  groupLessonsByDate
} from "../utils/dashboardHelpers";
import { createDefaultCalendar } from "../utils/calendarUtils";

// Create a new tutor
export const createTutor = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { firstname, lastname, email, password, phoneno, ...otherFields } = req.body;

    if (!firstname || !lastname || !email || !password) {
      res.status(400).json({
        message: "Firstname, lastname, email, and password are required.",
      });
      return;
    }

    // Check if email already exists
    const existingUser = await getProfile({ email });
    if (existingUser) {
      res.status(400).json({ message: "Email already in use." });
      return;
    }

    // Create new tutor
    const TutorModel = getProfileModelByRole("tutor");
    const hashedPassword = await hashPassword(password);

    const tutorData = {
      firstname,
      lastname,
      email,
      password: hashedPassword,
      phoneno,
      role: "tutor",
      ...otherFields
    };

    const tutor = await TutorModel.create(tutorData);

    // Create default calendar for the new tutor
    try {
      const userName = `${firstname} ${lastname}`.trim();

      await createDefaultCalendar({
        userId: tutor._id,
        userType: 'tutor',
        userName: userName,
        timezone: tutor.timezone || 'UTC'
      });

      console.log(`Created default calendar for new tutor: ${tutor._id}`);
    } catch (calendarError) {
      console.error(`Failed to create default calendar for tutor ${tutor._id}:`, calendarError);
      // Don't fail tutor creation if calendar creation fails
    }

    res.status(201).json({ success: true, data: tutor });
  } catch (error: any) {
    res
      .status(500)
      .json({ success: false, message: error.message || "Server Error" });
  }
};

export const getAllTutors = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    const TutorModel = getProfileModelByRole("tutor");
    const tutors = await TutorModel.find({}).sort({ createdAt: -1 });

    res.status(200).json({ success: true, data: tutors });
  } catch (error: any) {
    res
      .status(500)
      .json({ success: false, message: error.message || "Server Error" });
  }
};

// Delete a tutor
export const deleteTutor = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    if (!id) {
      res.status(400).json({ success: false, message: "Tutor ID is required." });
      return;
    }

    const TutorModel = getProfileModelByRole("tutor");
    const tutor = await TutorModel.findByIdAndDelete(id);

    if (!tutor) {
      res.status(404).json({ success: false, message: "Tutor not found." });
      return;
    }

    res
      .status(200)
      .json({ success: true, message: "Tutor deleted successfully." });
  } catch (error: any) {
    res
      .status(500)
      .json({ success: false, message: error.message || "Server Error" });
  }
};

export const getOverviewInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;

    const periodStart = startOfPeriod(period as string);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalEarnings = await TransactionModel.aggregate([
      { $match: { userId: tutorId, createdAt: { $lte: periodEnd } } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);
    const periodEarnings = await TransactionModel.aggregate([
      { $match: { userId: tutorId, createdAt: { $gte: periodStart, $lte: periodEnd } } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    const totalLessons = await Lesson.countDocuments({
      tutorId,
      startDateTime: { $lte: periodEnd },
    });
    const periodLessons = await Lesson.countDocuments({
      tutorId,
      startDateTime: { $gte: periodStart, $lte: periodEnd },
    });

    const totalActiveStudents = await Lesson.distinct("studentId", {
      tutorId,
      startDateTime: { $lte: periodEnd },
    }).then((ids) => ids.length);

    const periodActiveStudents = await Lesson.distinct("studentId", {
      tutorId,
      startDateTime: { $gte: periodStart, $lte: periodEnd },
    }).then((ids) => ids.length);

    const totalStudents = await Student.countDocuments({
      createdAt: { $lte: periodEnd },
      tutorId,
    });
    const periodNewStudents = await Student.countDocuments({
      createdAt: { $gte: periodStart, $lte: periodEnd },
      tutorId,
    });

    createOkResponse(res, {
      data: {
        earnings: {
          total: totalEarnings[0]?.total || 0,
          value: periodEarnings[0]?.total || 0,
          percentage: (
            ((periodEarnings[0]?.total || 0) / (totalEarnings[0]?.total || 1)) *
            100
          ).toFixed(2),
        },
        lessons: {
          total: totalLessons,
          value: periodLessons,
          percentage: ((periodLessons / (totalLessons || 1)) * 100).toFixed(2),
        },
        activeStudents: {
          total: totalActiveStudents,
          value: periodActiveStudents,
          percentage: (periodActiveStudents / (totalActiveStudents || 1)) * 100,
        },
        newStudents: {
          total: totalStudents,
          value: periodNewStudents,
          percentage: (periodNewStudents / (totalStudents || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getLessonsInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;
    const days = parseInt(period as string, 10);
    const periodStart = startOfPeriod(days);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalCancelled = await Lesson.countDocuments({
      tutorId,
      status: "cancelled",
      startDateTime: { $lte: periodEnd },
    });
    const periodCancelled = await Lesson.countDocuments({
      tutorId,
      status: "cancelled",
      startDateTime: { $gte: periodStart, $lte: periodEnd },
    });

    const totalScheduled = await Lesson.countDocuments({
      tutorId,
      status: "scheduled",
      startDateTime: { $lte: periodEnd },
    });
    const periodScheduled = await Lesson.countDocuments({
      tutorId,
      status: "scheduled",
      startDateTime: { $gte: periodStart, $lte: periodEnd },
    });

    createOkResponse(res, {
      data: {
        cancelled: {
          total: totalCancelled,
          value: periodCancelled,
          percentage: (periodCancelled / (totalCancelled || 1)) * 100,
        },
        scheduled: {
          total: totalScheduled,
          value: periodScheduled,
          percentage: (periodScheduled / (totalScheduled || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getSubscriptionsInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;
    const days = parseInt(period as string, 10);
    const periodStart = startOfPeriod(days);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalProfileViews = await Student.aggregate([
      { $match: { tutorId, "profileViews.date": { $lte: periodEnd } } },
      { $unwind: "$profileViews" },
      { $match: { "profileViews.date": { $lte: periodEnd } } },
      { $count: "count" },
    ]).then((res) => res[0]?.count || 0);

    const periodProfileViews = await Student.aggregate([
      {
        $match: {
          tutorId,
          "profileViews.date": { $gte: periodStart, $lte: periodEnd },
        },
      },
      { $unwind: "$profileViews" },
      {
        $match: { "profileViews.date": { $gte: periodStart, $lte: periodEnd } },
      },
      { $count: "count" },
    ]).then((res) => res[0]?.count || 0);

    const totalTrialLessons = await Lesson.countDocuments({
      tutorId,
      lessonType: "trial",
      startDateTime: { $lte: periodEnd },
    });
    const periodTrialLessons = await Lesson.countDocuments({
      tutorId,
      lessonType: "trial",
      startDateTime: { $gte: periodStart, $lte: periodEnd },
    });

    const totalSubscriptions = await Subscription.countDocuments({
      tutorId,
      status: 'active',
      createdAt: { $lte: periodEnd },
    });

    const periodSubscriptions = await Subscription.countDocuments({
      tutorId,
      status: 'active',
      createdAt: { $gte: periodStart, $lte: periodEnd },
    });

    createOkResponse(res, {
      data: {
        profileViews: {
          total: totalProfileViews,
          value: periodProfileViews,
          percentage: (periodProfileViews / (totalProfileViews || 1)) * 100,
        },
        trialLessons: {
          total: totalTrialLessons,
          value: periodTrialLessons,
          percentage: (periodTrialLessons / (totalTrialLessons || 1)) * 100,
        },
        newSubscriptions: {
          total: totalSubscriptions,
          value: periodSubscriptions,
          percentage: (periodSubscriptions / (totalSubscriptions || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

// Get comprehensive tutor dashboard stats
export const getTutorDashboardStats = async (req: AuthRequest, res: Response) => {
  try {
    const tutorId = req.user._id;

    // 1. Total Earnings - from transactions (lesson payouts) and escrow (released amounts)
    const totalEarningsFromTransactions = await TransactionModel.aggregate([
      {
        $match: {
          userId: tutorId,
          type: "lesson_payout",
          status: "completed"
        }
      },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    const totalEarningsFromEscrow = await EscrowModel.aggregate([
      {
        $match: {
          tutorId: tutorId,
          status: "released"
        }
      },
      { $group: { _id: null, total: { $sum: "$tutorPayout" } } },
    ]);

    const totalEarnings = (totalEarningsFromTransactions[0]?.total || 0) +
                         (totalEarningsFromEscrow[0]?.total || 0);

    // 2. Total number of lessons (completed lessons)
    const totalLessons = await Lesson.countDocuments({
      tutorId,
      status: "completed"
    });

    // 3. Total number of students (unique students who have had lessons with this tutor)
    const totalStudents = await Lesson.distinct("studentId", {
      tutorId
    }).then((ids) => ids.length);

    // 4. Total number of active students (students with active subscriptions to this tutor)
    const totalActiveStudents = await Subscription.distinct("studentId", {
      tutorId,
      status: "active"
    }).then((ids) => ids.length);

    // 5. Total hours taught (sum of completed lesson durations in hours)
    const totalHoursTaughtResult = await Lesson.aggregate([
      {
        $match: {
          tutorId: tutorId,
          status: "completed"
        }
      },
      {
        $group: {
          _id: null,
          totalMinutes: { $sum: "$duration" }
        }
      },
    ]);

    const totalHoursTaught = totalHoursTaughtResult[0]?.totalMinutes
      ? Math.round((totalHoursTaughtResult[0].totalMinutes / 60) * 100) / 100 // Convert to hours and round to 2 decimal places
      : 0;

    // 6. Get upcoming lessons (next 10)
    const upcomingLessons = await Lesson.find({
      tutorId: tutorId,
      status: { $in: ['scheduled', 'confirmed'] },
      startDateTime: { $gte: new Date() }
    })
    .populate('studentId', 'firstname lastname email profilePicture')
    .populate('subscriptionId', 'planType lessonsPerWeek')
    .sort({ startDateTime: 1 })
    .limit(10)
    .select('title subject startDateTime endDateTime duration timezone lessonType isTrialLesson studentId subscriptionId');

    // 7. Get tutor's calendars and schedules
    const tutorCalendars = await Calendar.find({
      ownerId: tutorId,
      ownerType: 'tutor',
      isActive: true
    }).select('name color hasSchedule defaultScheduleId');

    // 8. Get active schedules
    const activeSchedules = await Schedule.find({
      tutorId: tutorId,
      isActive: true
    })
    .populate('calendarId', 'name color')
    .select('name description timezone weeklySchedule isDefault');

    // 9. Get today's lessons
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const todaysLessons = await Lesson.find({
      tutorId: tutorId,
      startDateTime: { $gte: startOfDay, $lt: endOfDay },
      status: { $in: ['scheduled', 'confirmed', 'in_progress'] }
    })
    .populate('studentId', 'firstname lastname')
    .sort({ startDateTime: 1 })
    .select('title subject startDateTime endDateTime duration studentId status');

    createOkResponse(res, {
      data: {
        // Existing stats
        totalEarnings: {
          amount: totalEarnings,
          currency: "USD",
          formatted: `$${(totalEarnings / 100).toFixed(2)}` // Convert from cents to dollars
        },
        totalLessons,
        totalStudents,
        totalActiveStudents,
        totalHoursTaught,

        // New schedule and lesson data
        upcomingLessons: upcomingLessons.map(lesson => ({
          id: lesson._id,
          title: lesson.title,
          subject: lesson.subject,
          startDateTime: lesson.startDateTime,
          endDateTime: lesson.endDateTime,
          duration: lesson.duration,
          timezone: lesson.timezone,
          lessonType: lesson.lessonType,
          isTrialLesson: lesson.isTrialLesson,
          student: {
            id: lesson.studentId._id,
            name: `${lesson.studentId.firstname} ${lesson.studentId.lastname}`,
            email: lesson.studentId.email,
            profilePicture: lesson.studentId.profilePicture
          },
          subscription: lesson.subscriptionId ? {
            planType: lesson.subscriptionId.planType,
            lessonsPerWeek: lesson.subscriptionId.lessonsPerWeek
          } : null
        })),

        todaysLessons: todaysLessons.map(lesson => ({
          id: lesson._id,
          title: lesson.title,
          subject: lesson.subject,
          startDateTime: lesson.startDateTime,
          endDateTime: lesson.endDateTime,
          duration: lesson.duration,
          status: lesson.status,
          student: {
            id: lesson.studentId._id,
            name: `${lesson.studentId.firstname} ${lesson.studentId.lastname}`
          }
        })),

        schedule: {
          calendars: tutorCalendars.map(cal => ({
            id: cal._id,
            name: cal.name,
            color: cal.color,
            hasSchedule: cal.hasSchedule,
            defaultScheduleId: cal.defaultScheduleId
          })),
          activeSchedules: activeSchedules.map(schedule => ({
            id: schedule._id,
            name: schedule.name,
            description: schedule.description,
            timezone: schedule.timezone,
            isDefault: schedule.isDefault,
            calendar: schedule.calendarId ? {
              name: schedule.calendarId.name,
              color: schedule.calendarId.color
            } : null,
            weeklySchedule: schedule.weeklySchedule
          }))
        },

        summary: {
          upcomingLessonsCount: upcomingLessons.length,
          todaysLessonsCount: todaysLessons.length,
          activeCalendars: tutorCalendars.length,
          activeSchedules: activeSchedules.length
        }
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

// Get tutor's upcoming schedule and lessons
export const getTutorUpcomingSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can access their schedule', 403);
      return;
    }

    const tutorId = req.user._id;
    const { days = 7, includeCompleted = false } = req.query;

    // Calculate date range
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + parseInt(days as string));

    // Build lesson status filter
    const statusFilter = includeCompleted === 'true'
      ? ['scheduled', 'confirmed', 'in_progress', 'completed']
      : ['scheduled', 'confirmed', 'in_progress'];

    // Get lessons in the date range
    const lessons = await Lesson.find({
      tutorId: tutorId,
      startDateTime: { $gte: startDate, $lte: endDate },
      status: { $in: statusFilter }
    })
    .populate('studentId', 'firstname lastname email profilePicture')
    .populate('subscriptionId', 'planType lessonsPerWeek monthlyPrice')
    .sort({ startDateTime: 1 })
    .select('title subject description startDateTime endDateTime duration timezone status lessonType isTrialLesson studentId subscriptionId lessonNotes');

    // Get tutor's active schedules for context
    const activeSchedules = await Schedule.find({
      tutorId: tutorId,
      isActive: true
    })
    .populate('calendarId', 'name color')
    .select('name description timezone weeklySchedule isDefault calendarId');

    // Group lessons by date
    const lessonsByDate = lessons.reduce((acc, lesson) => {
      const dateKey = lesson.startDateTime.toISOString().split('T')[0];
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push({
        id: lesson._id,
        title: lesson.title,
        subject: lesson.subject,
        description: lesson.description,
        startDateTime: lesson.startDateTime,
        endDateTime: lesson.endDateTime,
        duration: lesson.duration,
        timezone: lesson.timezone,
        status: lesson.status,
        lessonType: lesson.lessonType,
        isTrialLesson: lesson.isTrialLesson,
        notes: lesson.lessonNotes,
        student: {
          id: lesson.studentId._id,
          name: `${lesson.studentId.firstname} ${lesson.studentId.lastname}`,
          email: lesson.studentId.email,
          profilePicture: lesson.studentId.profilePicture
        },
        subscription: lesson.subscriptionId ? {
          planType: lesson.subscriptionId.planType,
          lessonsPerWeek: lesson.subscriptionId.lessonsPerWeek,
          monthlyPrice: lesson.subscriptionId.monthlyPrice
        } : null
      });
      return acc;
    }, {} as Record<string, any[]>);

    createOkResponse(res, {
      data: {
        dateRange: {
          start: startDate,
          end: endDate,
          days: parseInt(days as string)
        },
        lessonsByDate,
        totalLessons: lessons.length,
        schedules: activeSchedules.map(schedule => ({
          id: schedule._id,
          name: schedule.name,
          description: schedule.description,
          timezone: schedule.timezone,
          isDefault: schedule.isDefault,
          calendar: schedule.calendarId ? {
            id: schedule.calendarId._id,
            name: schedule.calendarId.name,
            color: schedule.calendarId.color
          } : null,
          weeklySchedule: schedule.weeklySchedule
        })),
        summary: {
          totalLessons: lessons.length,
          scheduledLessons: lessons.filter(l => l.status === 'scheduled').length,
          confirmedLessons: lessons.filter(l => l.status === 'confirmed').length,
          completedLessons: lessons.filter(l => l.status === 'completed').length,
          trialLessons: lessons.filter(l => l.isTrialLesson).length,
          uniqueStudents: [...new Set(lessons.map(l => l.studentId._id.toString()))].length
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching tutor schedule:', error);
    createErrorResponse(res, error.message || 'Failed to fetch schedule', 500);
  }
};

// Get tutor's calendar view with lessons and schedule patterns
export const getTutorCalendarView = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can access their calendar', 403);
      return;
    }

    const tutorId = req.user._id;
    const {
      startDate,
      endDate,
      viewType = 'month',
      includeAvailability = true
    } = req.query;

    // Calculate date range
    const { startDate: defaultStart, endDate: defaultEnd } = getDateRange(30); // 30 days by default
    const start = startDate ? new Date(startDate as string) : defaultStart;
    const end = endDate ? new Date(endDate as string) : defaultEnd;

    // Get tutor's lessons in the date range
    const lessons = await Lesson.find({
      tutorId: tutorId,
      startDateTime: { $gte: start, $lte: end }
    })
    .populate('studentId', 'firstname lastname email profilePicture')
    .populate('subscriptionId', 'planType lessonsPerWeek monthlyPrice')
    .sort({ startDateTime: 1 })
    .select('title subject description startDateTime endDateTime duration timezone status lessonType isTrialLesson studentId subscriptionId lessonNotes');

    // Format lessons for display
    const formattedLessons = formatLessonsForDashboard(lessons, 'tutor');

    // Get tutor's calendars
    const tutorCalendars = await getUserCalendars(tutorId, 'tutor');

    // Get tutor's schedules
    const tutorSchedules = await getTutorSchedules(tutorId);

    // Group lessons by date for easier calendar display
    const lessonsByDate = groupLessonsByDate(formattedLessons);

    // Get availability information if requested
    let availabilityInfo = null;
    if (includeAvailability === 'true') {
      // Calculate basic availability metrics
      const totalScheduledHours = formattedLessons
        .filter(l => ['scheduled', 'confirmed', 'completed'].includes(l.status))
        .reduce((total, lesson) => total + lesson.duration, 0) / 60;

      availabilityInfo = {
        totalScheduledHours: Math.round(totalScheduledHours * 100) / 100,
        averageHoursPerDay: Math.round((totalScheduledHours / Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))) * 100) / 100,
        busyDays: Object.keys(lessonsByDate).length,
        totalDaysInRange: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
      };
    }

    createOkResponse(res, {
      data: {
        dateRange: {
          start,
          end,
          viewType
        },
        lessons: formattedLessons,
        lessonsByDate,
        calendars: tutorCalendars,
        schedules: tutorSchedules,
        availability: availabilityInfo,
        summary: {
          totalLessons: formattedLessons.length,
          upcomingLessons: formattedLessons.filter(l =>
            new Date(l.startDateTime) > new Date() &&
            ['scheduled', 'confirmed'].includes(l.status)
          ).length,
          completedLessons: formattedLessons.filter(l => l.status === 'completed').length,
          trialLessons: formattedLessons.filter(l => l.isTrialLesson).length,
          uniqueStudents: [...new Set(formattedLessons.map(l => l.student?.id))].filter(Boolean).length,
          totalRevenue: formattedLessons
            .filter(l => l.status === 'completed' && l.subscription)
            .reduce((total, lesson) => {
              // Rough calculation based on monthly price and lessons per week
              const weeklyRate = (lesson.subscription?.monthlyPrice || 0) / 4;
              const lessonRate = weeklyRate / (lesson.subscription?.lessonsPerWeek || 1);
              return total + lessonRate;
            }, 0)
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching tutor calendar view:', error);
    createErrorResponse(res, error.message || 'Failed to fetch calendar view', 500);
  }
};
