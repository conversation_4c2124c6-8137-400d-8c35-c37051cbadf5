import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calendar';
import Subscription from '../models/subscription.model';
import Student from '../models/student';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

// Create a calendar (student or tutor)
export const createCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || (req.user.role !== 'tutor' && req.user.role !== 'student')) {
      createErrorResponse(res, 'Only tutors and students can create calendars', 403);
      return;
    }

    const {
      name,
      description,
      color,
      isShared,
      timezone
    } = req.body;

    if (!name) {
      createErrorResponse(res, 'Calendar name is required', 400);
      return;
    }

    // Check if user already has a calendar with this name
    const existingCalendar = await Calendar.findOne({
      ownerId: req.user._id,
      ownerType: req.user.role,
      name: name.trim()
    });

    if (existingCalendar) {
      createErrorResponse(res, 'You already have a calendar with this name', 409);
      return;
    }

    const calendar = new Calendar({
      ownerId: req.user._id,
      ownerType: req.user.role,
      name: name.trim(),
      description,
      color: color || (req.user.role === 'tutor' ? '#3B82F6' : '#10B981'),
      isShared: isShared ?? (req.user.role === 'tutor' ? true : false),
      timezone: timezone || 'UTC'
    });

    await calendar.save();

    const populatedCalendar = await Calendar.findById(calendar._id)
      .populate('ownerId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: 'Calendar created successfully',
      data: populatedCalendar
    });

  } catch (error) {
    console.error('Error creating calendar:', error);
    createErrorResponse(res, 'Failed to create calendar', 500);
  }
};

// Get all calendars of a tutor (for learner or tutor)
export const getTutorCalendars = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view tutor calendars', 403);
      return;
    }

    const tutorId = req.params.tutorId;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Invalid tutor ID format', 400);
      return;
    }

    // Check if student has subscription or trial eligibility with this tutor
    const [subscription, student] = await Promise.all([
      Subscription.findOne({
        studentId: req.user._id,
        tutorId: tutorId,
        status: 'active'
      }),
      Student.findById(req.user._id)
    ]);

    const hasSubscription = !!subscription;
    const canUseTrial = !student?.hasUsedFreeTrial;

    if (!hasSubscription && !canUseTrial) {
      createErrorResponse(res, 'You need an active subscription or trial eligibility to view this tutor\'s calendars', 403);
      return;
    }

    const calendars = await Calendar.find({
      ownerId: tutorId,
      ownerType: 'tutor',
      isShared: true,
      isActive: true
    }).populate('ownerId', 'firstname lastname email avatar')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: calendars,
      accessInfo: {
        hasSubscription,
        canUseTrial,
        remainingLessons: subscription?.remainingLessons || 0
      }
    });

  } catch (error) {
    console.error('Error fetching tutor calendars:', error);
    createErrorResponse(res, 'Failed to fetch calendars', 500);
  }
};

// Update calendar (only tutor who owns the calendar)
export const updateCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update calendars', 403);
      return;
    }

    const calendarId = req.params.id;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.ownerId.toString() !== (req.user._id as Types.ObjectId).toString() ||
        calendar.ownerType !== req.user.role) {
      createErrorResponse(res, 'Not authorized to update this calendar', 403);
      return;
    }

    const {
      name,
      description,
      color,
      isShared,
      timezone,
      isActive
    } = req.body;

    // Update fields
    if (name !== undefined) calendar.name = name.trim();
    if (description !== undefined) calendar.description = description;
    if (color !== undefined) calendar.color = color;
    if (isShared !== undefined) calendar.isShared = isShared;
    if (timezone !== undefined) calendar.timezone = timezone;
    if (isActive !== undefined) calendar.isActive = isActive;

    await calendar.save();

    const updatedCalendar = await Calendar.findById(calendar._id)
      .populate('ownerId', 'firstname lastname email');

    res.json({
      success: true,
      message: 'Calendar updated successfully',
      data: updatedCalendar
    });

  } catch (error) {
    console.error('Error updating calendar:', error);
    createErrorResponse(res, 'Failed to update calendar', 500);
  }
};

// Delete calendar (only owner can delete)
export const deleteCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || (req.user.role !== 'tutor' && req.user.role !== 'student')) {
      createErrorResponse(res, 'Only calendar owners can delete calendars', 403);
      return;
    }

    const calendarId = req.params.id;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.ownerId.toString() !== (req.user._id as Types.ObjectId).toString() ||
        calendar.ownerType !== req.user.role) {
      createErrorResponse(res, 'Not authorized to delete this calendar', 403);
      return;
    }

    // Calendar can be deleted directly since we removed events

    await calendar.deleteOne();

    res.json({
      success: true,
      message: 'Calendar deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting calendar:', error);
    createErrorResponse(res, 'Failed to delete calendar', 500);
  }
};

// Get user's own calendars (student or tutor)
export const getMyCalendars = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || (req.user.role !== 'tutor' && req.user.role !== 'student')) {
      createErrorResponse(res, 'Only tutors and students can view their calendars', 403);
      return;
    }

    const { includeInactive = false } = req.query;

    const query: any = {
      ownerId: req.user._id,
      ownerType: req.user.role
    };
    if (!includeInactive) {
      query.isActive = true;
    }

    const calendars = await Calendar.find(query)
      .populate('defaultScheduleId', 'name isActive')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: calendars,
      summary: {
        totalCalendars: calendars.length,
        activeCalendars: calendars.filter(c => c.isActive).length,
        scheduledCalendars: calendars.filter(c => c.hasSchedule).length
      }
    });

  } catch (error) {
    console.error('Error fetching tutor calendars:', error);
    createErrorResponse(res, 'Failed to fetch calendars', 500);
  }
};
