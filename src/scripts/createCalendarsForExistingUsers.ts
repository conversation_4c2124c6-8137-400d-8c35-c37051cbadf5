import mongoose from 'mongoose';
import { Calendar } from '../models/calendar';
import Student from '../models/student';
import <PERSON><PERSON> from '../models/tutor';
import { createDefaultCalendar } from '../utils/calendarUtils';

/**
 * Migration script to create default calendars for existing users who don't have them
 * This script should be run once after implementing the automatic calendar creation feature
 */

interface UserWithoutCalendar {
  _id: mongoose.Types.ObjectId;
  firstname: string;
  lastname: string;
  email: string;
  role: 'student' | 'tutor';
  timezone?: string;
}

async function findUsersWithoutCalendars(): Promise<UserWithoutCalendar[]> {
  console.log('Finding users without calendars...');
  
  // Get all students
  const students = await Student.find({}, 'firstname lastname email timezone').lean();
  
  // Get all tutors
  const tutors = await Tutor.find({}, 'firstname lastname email timezone').lean();
  
  // Combine all users
  const allUsers: UserWithoutCalendar[] = [
    ...students.map(s => ({ ...s, role: 'student' as const })),
    ...tutors.map(t => ({ ...t, role: 'tutor' as const }))
  ];
  
  console.log(`Found ${allUsers.length} total users (${students.length} students, ${tutors.length} tutors)`);
  
  // Check which users don't have calendars
  const usersWithoutCalendars: UserWithoutCalendar[] = [];
  
  for (const user of allUsers) {
    const existingCalendar = await Calendar.findOne({
      ownerId: user._id,
      ownerType: user.role,
      isActive: true
    });
    
    if (!existingCalendar) {
      usersWithoutCalendars.push(user);
    }
  }
  
  console.log(`Found ${usersWithoutCalendars.length} users without calendars`);
  return usersWithoutCalendars;
}

async function createCalendarsForUsers(users: UserWithoutCalendar[]): Promise<void> {
  console.log(`Creating calendars for ${users.length} users...`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const user of users) {
    try {
      const userName = `${user.firstname} ${user.lastname}`.trim();
      
      const calendar = await createDefaultCalendar({
        userId: user._id,
        userType: user.role,
        userName: userName,
        timezone: user.timezone || 'UTC'
      });
      
      if (calendar) {
        console.log(`✅ Created calendar for ${user.role} ${userName} (${user._id})`);
        successCount++;
      } else {
        console.log(`❌ Failed to create calendar for ${user.role} ${userName} (${user._id})`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error creating calendar for ${user.role} ${user.firstname} ${user.lastname} (${user._id}):`, error);
      errorCount++;
    }
    
    // Add a small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n📊 Migration Results:`);
  console.log(`✅ Successfully created: ${successCount} calendars`);
  console.log(`❌ Failed to create: ${errorCount} calendars`);
  console.log(`📈 Success rate: ${((successCount / users.length) * 100).toFixed(1)}%`);
}

async function validateCalendarCreation(): Promise<void> {
  console.log('\n🔍 Validating calendar creation...');
  
  // Count total users
  const [studentCount, tutorCount] = await Promise.all([
    Student.countDocuments(),
    Tutor.countDocuments()
  ]);
  
  // Count calendars
  const [studentCalendarCount, tutorCalendarCount] = await Promise.all([
    Calendar.countDocuments({ ownerType: 'student', isActive: true }),
    Calendar.countDocuments({ ownerType: 'tutor', isActive: true })
  ]);
  
  console.log(`👥 Total users: ${studentCount + tutorCount} (${studentCount} students, ${tutorCount} tutors)`);
  console.log(`📅 Total calendars: ${studentCalendarCount + tutorCalendarCount} (${studentCalendarCount} student calendars, ${tutorCalendarCount} tutor calendars)`);
  
  if (studentCount === studentCalendarCount && tutorCount === tutorCalendarCount) {
    console.log('✅ All users have calendars!');
  } else {
    console.log('⚠️  Some users still don\'t have calendars');
    console.log(`   Missing student calendars: ${studentCount - studentCalendarCount}`);
    console.log(`   Missing tutor calendars: ${tutorCount - tutorCalendarCount}`);
  }
}

export async function runCalendarMigration(): Promise<void> {
  try {
    console.log('🚀 Starting calendar migration for existing users...\n');
    
    // Find users without calendars
    const usersWithoutCalendars = await findUsersWithoutCalendars();
    
    if (usersWithoutCalendars.length === 0) {
      console.log('✅ All users already have calendars. No migration needed.');
      return;
    }
    
    // Create calendars for users who don't have them
    await createCalendarsForUsers(usersWithoutCalendars);
    
    // Validate the migration
    await validateCalendarCreation();
    
    console.log('\n🎉 Calendar migration completed!');
    
  } catch (error) {
    console.error('💥 Calendar migration failed:', error);
    throw error;
  }
}

// CLI execution
if (require.main === module) {
  // Connect to MongoDB
  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/convolly';
  
  mongoose.connect(MONGODB_URI)
    .then(async () => {
      console.log('📡 Connected to MongoDB');
      await runCalendarMigration();
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 MongoDB connection failed:', error);
      process.exit(1);
    });
}

export default runCalendarMigration;
