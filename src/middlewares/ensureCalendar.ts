import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from './auth';
import { createErrorResponse } from './errorHandler';
import { ensureUserHasCalendar } from '../utils/calendarUtils';
import { logError } from '../utils/error';

/**
 * Middleware to ensure the authenticated user has a default calendar
 * Creates one if it doesn't exist
 */
export const ensureUserCalendar = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Ensure user has a calendar
    const calendar = await ensureUserHasCalendar(
      req.user._id,
      req.user.role as 'student' | 'tutor',
      `${req.user.firstname} ${req.user.lastname}`.trim(),
      req.user.timezone
    );

    if (!calendar) {
      logError('Failed to ensure user has calendar', 'calendar', {
        userId: req.user._id,
        userRole: req.user.role
      });
      
      // Don't fail the request, just log the error
      console.warn(`Could not ensure calendar for user ${req.user._id}`);
    }

    // Add calendar info to request for downstream use
    req.userCalendar = calendar;
    
    next();
  } catch (error: any) {
    logError('Error in ensureUserCalendar middleware', 'calendar', {
      userId: req.user?._id,
      userRole: req.user?.role,
      error: error.message
    });
    
    // Don't fail the request, just continue without calendar
    console.error('ensureUserCalendar middleware error:', error);
    next();
  }
};

/**
 * Middleware specifically for calendar-related routes
 * Ensures user has a calendar and fails if creation fails
 */
export const requireUserCalendar = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Ensure user has a calendar
    const calendar = await ensureUserHasCalendar(
      req.user._id,
      req.user.role as 'student' | 'tutor',
      `${req.user.firstname} ${req.user.lastname}`.trim(),
      req.user.timezone
    );

    if (!calendar) {
      createErrorResponse(res, 'Failed to access calendar. Please try again later.', 500);
      return;
    }

    // Add calendar info to request for downstream use
    req.userCalendar = calendar;
    
    next();
  } catch (error: any) {
    logError('Error in requireUserCalendar middleware', 'calendar', {
      userId: req.user?._id,
      userRole: req.user?.role,
      error: error.message
    });
    
    createErrorResponse(res, 'Calendar access error. Please try again later.', 500);
  }
};

/**
 * Middleware for lesson scheduling that ensures both student and tutor have calendars
 */
export const ensureBothUsersHaveCalendars = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Ensure current user has a calendar
    const userCalendar = await ensureUserHasCalendar(
      req.user._id,
      req.user.role as 'student' | 'tutor',
      `${req.user.firstname} ${req.user.lastname}`.trim(),
      req.user.timezone
    );

    if (!userCalendar) {
      createErrorResponse(res, 'Failed to access your calendar. Please try again later.', 500);
      return;
    }

    // Add calendar info to request
    req.userCalendar = userCalendar;
    
    // If tutorId is provided in the request (for lesson scheduling), ensure tutor has calendar
    const tutorId = req.body.tutorId || req.params.tutorId;
    if (tutorId && req.user.role === 'student') {
      try {
        // Note: We don't have tutor details here, so we'll let the lesson controller handle this
        // This middleware just ensures the current user has a calendar
      } catch (tutorCalendarError) {
        console.warn(`Could not ensure tutor ${tutorId} has calendar:`, tutorCalendarError);
        // Don't fail the request, let the lesson controller handle it
      }
    }
    
    next();
  } catch (error: any) {
    logError('Error in ensureBothUsersHaveCalendars middleware', 'calendar', {
      userId: req.user?._id,
      userRole: req.user?.role,
      error: error.message
    });
    
    createErrorResponse(res, 'Calendar access error. Please try again later.', 500);
  }
};

// Extend AuthRequest interface to include calendar
declare global {
  namespace Express {
    interface Request {
      userCalendar?: any;
    }
  }
}

export interface CalendarAuthRequest extends AuthRequest {
  userCalendar?: any;
}
