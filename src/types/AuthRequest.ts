import { Request } from 'express';
import { Types } from 'mongoose';
import { USER_PROFILE_TYPE } from '../utils/profile';
import { ISubscription } from '../models/subscription.model';

// Basic auth user from JWT token
export interface AuthUser {
  id: string;  // User ID from JWT token
  role: 'student' | 'tutor' | 'admin';
}

// Extended interface for authenticated requests
// After isAuthenticated middleware, req.user will be a full profile object
export interface AuthRequest extends Request {
  user?: USER_PROFILE_TYPE;  // Full profile object (Student/Tutor/Admin)
  subscription?: ISubscription;  // Subscription info added by validation middleware
  tutorId?: Types.ObjectId;  // Tutor ID added by validation middleware
  isTrialBooking?: boolean;  // Whether this is a trial booking
  trialEligibility?: {  // Trial eligibility info
    isEligible: boolean;
    hasUsedTrial: boolean;
  };
}