import { Types } from 'mongoose';
import { Calendar } from '../models/calendar';
import { logError } from './error';

export interface CreateDefaultCalendarOptions {
  userId: Types.ObjectId;
  userType: 'student' | 'tutor';
  userName?: string;
  timezone?: string;
  customCalendarId?: string; // Optional custom calendar ID
}

/**
 * Creates a default calendar for a new user (student or tutor)
 * Uses the user's ID as the calendar ID if no custom ID is provided
 */
export const createDefaultCalendar = async (options: CreateDefaultCalendarOptions): Promise<any> => {
  try {
    const { userId, userType, userName, timezone = 'UTC', customCalendarId } = options;

    // Check if user already has a calendar
    const existingCalendar = await Calendar.findOne({
      ownerId: userId,
      ownerType: userType,
      isActive: true
    });

    if (existingCalendar) {
      console.log(`User ${userId} already has a calendar: ${existingCalendar._id}`);
      return existingCalendar;
    }

    // Determine calendar name based on user type
    const calendarName = userType === 'tutor' 
      ? `${userName ? `${userName}'s` : 'My'} Teaching Calendar`
      : `${userName ? `${userName}'s` : 'My'} Learning Calendar`;

    // Create calendar data
    const calendarData = {
      _id: customCalendarId ? new Types.ObjectId(customCalendarId) : userId, // Use userId as calendar ID
      ownerId: userId,
      ownerType: userType,
      name: calendarName,
      description: userType === 'tutor' 
        ? 'Default calendar for managing teaching schedule and lessons'
        : 'Default calendar for managing learning schedule and lessons',
      color: userType === 'tutor' ? '#10B981' : '#3B82F6', // Green for tutors, Blue for students
      isShared: userType === 'tutor', // Tutors share calendars by default, students don't
      timezone: timezone,
      isActive: true,
      hasSchedule: userType === 'tutor', // Tutors typically have schedules
      scheduleSettings: {
        autoGenerateFromSchedule: userType === 'tutor',
        generateDaysAhead: 30,
        allowScheduleOverrides: true
      },
      displaySettings: {
        showSchedulePattern: userType === 'tutor',
        showAvailabilityOnly: userType === 'tutor', // Show availability to students
        groupSimilarSlots: true
      }
    };

    const calendar = new Calendar(calendarData);
    await calendar.save();

    console.log(`Created default calendar for ${userType} ${userId}: ${calendar._id}`);
    return calendar;

  } catch (error: any) {
    logError(`Failed to create default calendar for user ${options.userId}`, 'calendar', {
      userId: options.userId,
      userType: options.userType,
      error: error.message
    });
    
    // Don't throw error to prevent registration failure
    console.error('Calendar creation failed:', error);
    return null;
  }
};

/**
 * Creates calendars for multiple users in batch
 */
export const createDefaultCalendarsForUsers = async (
  users: Array<{ id: Types.ObjectId; type: 'student' | 'tutor'; name?: string; timezone?: string }>
): Promise<any[]> => {
  const results = [];
  
  for (const user of users) {
    const calendar = await createDefaultCalendar({
      userId: user.id,
      userType: user.type,
      userName: user.name,
      timezone: user.timezone
    });
    results.push(calendar);
  }
  
  return results;
};

/**
 * Ensures a user has a default calendar, creates one if missing
 */
export const ensureUserHasCalendar = async (
  userId: Types.ObjectId,
  userType: 'student' | 'tutor',
  userName?: string,
  timezone?: string
): Promise<any> => {
  try {
    // Check if user has any active calendar
    const existingCalendar = await Calendar.findOne({
      ownerId: userId,
      ownerType: userType,
      isActive: true
    });

    if (existingCalendar) {
      return existingCalendar;
    }

    // Create default calendar if none exists
    return await createDefaultCalendar({
      userId,
      userType,
      userName,
      timezone
    });

  } catch (error: any) {
    logError(`Failed to ensure calendar exists for user ${userId}`, 'calendar', {
      userId,
      userType,
      error: error.message
    });
    return null;
  }
};

/**
 * Updates calendar settings based on user preferences
 */
export const updateCalendarSettings = async (
  calendarId: Types.ObjectId,
  settings: {
    name?: string;
    description?: string;
    color?: string;
    timezone?: string;
    isShared?: boolean;
    scheduleSettings?: any;
    displaySettings?: any;
  }
): Promise<any> => {
  try {
    const calendar = await Calendar.findByIdAndUpdate(
      calendarId,
      { $set: settings },
      { new: true, runValidators: true }
    );

    if (!calendar) {
      throw new Error('Calendar not found');
    }

    return calendar;
  } catch (error: any) {
    logError(`Failed to update calendar settings for ${calendarId}`, 'calendar', {
      calendarId,
      settings,
      error: error.message
    });
    throw error;
  }
};

/**
 * Deactivates a user's calendar (soft delete)
 */
export const deactivateUserCalendar = async (
  userId: Types.ObjectId,
  userType: 'student' | 'tutor'
): Promise<boolean> => {
  try {
    const result = await Calendar.updateMany(
      { ownerId: userId, ownerType: userType },
      { $set: { isActive: false } }
    );

    return result.modifiedCount > 0;
  } catch (error: any) {
    logError(`Failed to deactivate calendar for user ${userId}`, 'calendar', {
      userId,
      userType,
      error: error.message
    });
    return false;
  }
};

/**
 * Gets user's default calendar
 */
export const getUserDefaultCalendar = async (
  userId: Types.ObjectId,
  userType: 'student' | 'tutor'
): Promise<any> => {
  try {
    const calendar = await Calendar.findOne({
      ownerId: userId,
      ownerType: userType,
      isActive: true
    }).sort({ createdAt: 1 }); // Get the first (oldest) calendar as default

    return calendar;
  } catch (error: any) {
    logError(`Failed to get default calendar for user ${userId}`, 'calendar', {
      userId,
      userType,
      error: error.message
    });
    return null;
  }
};

/**
 * Validates calendar ownership
 */
export const validateCalendarOwnership = async (
  calendarId: Types.ObjectId,
  userId: Types.ObjectId,
  userType: 'student' | 'tutor'
): Promise<boolean> => {
  try {
    const calendar = await Calendar.findOne({
      _id: calendarId,
      ownerId: userId,
      ownerType: userType,
      isActive: true
    });

    return !!calendar;
  } catch (error: any) {
    logError(`Failed to validate calendar ownership`, 'calendar', {
      calendarId,
      userId,
      userType,
      error: error.message
    });
    return false;
  }
};
