# Schedule Types Documentation

The calendar and schedule system now supports multiple schedule types to accommodate different tutoring patterns and preferences.

## Schedule Types

### 1. Weekly Schedule (`scheduleType: 'weekly'`)
**Default type** - Repeats the same pattern every week.

```json
{
  "scheduleType": "weekly",
  "weeklySchedule": [
    {
      "dayOfWeek": 1,
      "isWorkingDay": true,
      "timeSlots": [
        {
          "startTime": "09:00",
          "endTime": "10:00",
          "isAvailable": true,
          "sessionType": "lesson",
          "price": 50
        },
        {
          "startTime": "14:00",
          "endTime": "15:00",
          "isAvailable": true,
          "sessionType": "lesson"
        }
      ]
    }
  ]
}
```

### 2. Daily Schedule (`scheduleType: 'daily'`)
Same schedule pattern repeated every day.

```json
{
  "scheduleType": "daily",
  "dailySchedule": {
    "dayOfWeek": 0,
    "isWorkingDay": true,
    "timeSlots": [
      {
        "startTime": "10:00",
        "endTime": "11:00",
        "isAvailable": true,
        "sessionType": "lesson"
      }
    ]
  }
}
```

### 3. Monthly Schedule (`scheduleType: 'monthly'`)
Specific patterns that repeat monthly (e.g., first Monday of each month).

```json
{
  "scheduleType": "monthly",
  "monthlySchedule": [
    {
      "weekOfMonth": 1,
      "dayOfWeek": 1,
      "timeSlots": [
        {
          "startTime": "15:00",
          "endTime": "16:00",
          "isAvailable": true,
          "sessionType": "lesson"
        }
      ]
    }
  ]
}
```

### 4. Custom Schedule (`scheduleType: 'custom'`)
Specific dates with custom availability.

```json
{
  "scheduleType": "custom",
  "customSchedule": {
    "specificDates": [
      {
        "date": "2024-01-15T00:00:00.000Z",
        "timeSlots": [
          {
            "startTime": "13:00",
            "endTime": "14:00",
            "isAvailable": true,
            "sessionType": "lesson"
          }
        ]
      }
    ]
  }
}
```

### 5. Flexible Schedule (`scheduleType: 'flexible'`)
For tutors who prefer booking-based scheduling.

```json
{
  "scheduleType": "flexible",
  "flexibleSchedule": {
    "availableHoursPerWeek": 20,
    "preferredTimeSlots": [
      {
        "startTime": "09:00",
        "endTime": "17:00",
        "isAvailable": true,
        "sessionType": "lesson"
      }
    ],
    "minimumNoticeHours": 24
  }
}
```

## API Usage

### Creating a Schedule

```javascript
POST /api/schedules
{
  "calendarId": "calendar_id_here",
  "name": "My Teaching Schedule",
  "description": "Weekly availability for lessons",
  "timezone": "UTC",
  "scheduleType": "weekly",
  "weeklySchedule": [...],
  "settings": {
    "autoGenerateEvents": true,
    "defaultSessionDuration": 60,
    "bufferTimeBetweenSessions": 15
  },
  "isDefault": true
}
```

### Updating a Schedule

```javascript
PUT /api/schedules/:scheduleId
{
  "scheduleType": "daily",
  "dailySchedule": {...}
}
```

## Schedule Type Use Cases

- **Weekly**: Regular tutors with consistent weekly availability
- **Daily**: Tutors available every day with the same time slots
- **Monthly**: Specialized tutors available on specific recurring dates
- **Custom**: One-off sessions or irregular availability
- **Flexible**: Tutors who prefer on-demand booking with minimum notice

## Notes

- When changing schedule types, the corresponding schedule data must be provided
- The system automatically calculates availability based on the schedule type
- Exceptions can be added to any schedule type for holidays or special circumstances
- Flexible schedules work best with booking systems that allow advance scheduling
