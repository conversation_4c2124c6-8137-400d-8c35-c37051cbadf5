# Dashboard API Documentation

## Overview
This document describes the dashboard APIs for both students (learners) and tutors, including comprehensive statistics, upcoming lessons, and calendar integration.

## Student Dashboard APIs

### 1. Student Dashboard Stats
Get comprehensive dashboard statistics for students.

**Endpoint:** `GET /api/student/dashboard/stats`

**Authentication:** Required (Student role)

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptions": {
      "active": 2,
      "total": 3,
      "totalRemainingLessons": 15,
      "activeSubscriptions": [
        {
          "id": "subscription_id",
          "tutorName": "<PERSON> Doe",
          "tutorEmail": "<EMAIL>",
          "tutorSubjects": ["Math", "Physics"],
          "tutorRating": 4.8,
          "planType": "2_lessons_weekly",
          "lessonsPerWeek": 2,
          "remainingLessons": 8,
          "currentPeriodEnd": "2024-01-31T23:59:59.999Z",
          "monthlyPrice": 200,
          "status": "active"
        }
      ]
    },
    "lessons": {
      "total": 25,
      "completed": 20,
      "upcoming": 3,
      "cancelled": 2,
      "totalHoursLearned": 33.5,
      "recentLessons": [...]
    },
    "trialStatus": {
      "globalTrialUsed": false,
      "hasUsedFreeTrial": false,
      "availableTrials": 0
    },
    "learningProgress": {
      "totalHoursLearned": 33.5,
      "lessonsCompleted": 20,
      "averageLessonDuration": 60,
      "activeTutors": 2
    }
  }
}
```

### 2. Student Upcoming Lessons
Get upcoming lessons for a student.

**Endpoint:** `GET /api/student/dashboard/upcoming-lessons`

**Query Parameters:**
- `limit` (optional): Number of lessons to return (default: 10)
- `startDate` (optional): Start date filter
- `endDate` (optional): End date filter

**Response:**
```json
{
  "success": true,
  "data": {
    "lessons": [
      {
        "id": "lesson_id",
        "title": "Math Lesson",
        "subject": "Mathematics",
        "startDateTime": "2024-01-15T10:00:00.000Z",
        "endDateTime": "2024-01-15T11:00:00.000Z",
        "duration": 60,
        "timezone": "UTC",
        "lessonType": "regular",
        "isTrialLesson": false,
        "tutor": {
          "id": "tutor_id",
          "name": "John Doe",
          "email": "<EMAIL>",
          "subjects": ["Math", "Physics"]
        }
      }
    ],
    "total": 3,
    "hasMore": false
  }
}
```

### 3. Student Calendar View
Get calendar view with lessons and schedule information.

**Endpoint:** `GET /api/student/dashboard/calendar`

**Query Parameters:**
- `startDate` (optional): Start date for calendar view
- `endDate` (optional): End date for calendar view
- `viewType` (optional): View type (month, week, day) - default: month
- `includeTutorSchedules` (optional): Include tutor availability - default: false

## Tutor Dashboard APIs

### 1. Enhanced Tutor Dashboard Stats
Get comprehensive dashboard statistics including schedule information.

**Endpoint:** `GET /api/tutor/:id/insight/dashboard-stats`

**Authentication:** Required (Tutor role)

**Response:**
```json
{
  "success": true,
  "data": {
    "totalEarnings": {
      "amount": 15000,
      "currency": "USD",
      "formatted": "$150.00"
    },
    "totalLessons": 25,
    "totalStudents": 8,
    "totalActiveStudents": 5,
    "totalHoursTaught": 20.83,
    "upcomingLessons": [
      {
        "id": "lesson_id",
        "title": "Math Lesson",
        "subject": "Mathematics",
        "startDateTime": "2024-01-15T10:00:00.000Z",
        "endDateTime": "2024-01-15T11:00:00.000Z",
        "duration": 60,
        "student": {
          "id": "student_id",
          "name": "Jane Smith",
          "email": "<EMAIL>"
        }
      }
    ],
    "todaysLessons": [...],
    "schedule": {
      "calendars": [
        {
          "id": "calendar_id",
          "name": "Main Calendar",
          "color": "#4F46E5",
          "hasSchedule": true
        }
      ],
      "activeSchedules": [
        {
          "id": "schedule_id",
          "name": "Weekly Schedule",
          "timezone": "UTC",
          "isDefault": true,
          "weeklySchedule": {...}
        }
      ]
    },
    "summary": {
      "upcomingLessonsCount": 5,
      "todaysLessonsCount": 2,
      "activeCalendars": 1,
      "activeSchedules": 1
    }
  }
}
```

### 2. Tutor Upcoming Schedule
Get detailed upcoming schedule for a tutor.

**Endpoint:** `GET /api/tutor/:id/insight/upcoming-schedule`

**Query Parameters:**
- `days` (optional): Number of days to look ahead (default: 7)
- `includeCompleted` (optional): Include completed lessons (default: false)

### 3. Tutor Calendar View
Get calendar view with lessons, schedule patterns, and availability.

**Endpoint:** `GET /api/tutor/:id/insight/calendar`

**Query Parameters:**
- `startDate` (optional): Start date for calendar view
- `endDate` (optional): End date for calendar view
- `viewType` (optional): View type (month, week, day) - default: month
- `includeAvailability` (optional): Include availability metrics - default: true

## Authentication & Authorization

### Student Dashboard Access
- Students can only access their own dashboard data
- Authentication required with student role
- Subscription validation for viewing tutor calendars

### Tutor Dashboard Access
- Tutors can only access their own dashboard data
- Authentication required with tutor role
- Full access to own calendar and schedule data

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

Common HTTP status codes:
- `401`: Authentication required
- `403`: Access denied (wrong role or permissions)
- `404`: Resource not found
- `500`: Internal server error

## Rate Limiting

Dashboard endpoints are subject to standard rate limiting:
- 100 requests per 15 minutes per user
- Burst limit of 20 requests per minute

## Caching

Dashboard data is cached for performance:
- Student dashboard stats: 5 minutes
- Tutor dashboard stats: 5 minutes
- Calendar views: 2 minutes
- Upcoming lessons: 1 minute

## Usage Examples

### Get Student Dashboard
```javascript
const response = await fetch('/api/student/dashboard/stats', {
  headers: {
    'Authorization': 'Bearer <jwt_token>',
    'Content-Type': 'application/json'
  }
});
const data = await response.json();
```

### Get Tutor Calendar View
```javascript
const response = await fetch('/api/tutor/123/insight/calendar?viewType=week&includeAvailability=true', {
  headers: {
    'Authorization': 'Bearer <jwt_token>',
    'Content-Type': 'application/json'
  }
});
const data = await response.json();
```
