# Earnings and Withdrawal System

This document describes the comprehensive earnings tracking and withdrawal request system for tutors.

## Overview

The system automatically:
1. **Deducts fees from learners** when lessons are completed
2. **Tracks tutor earnings** with detailed breakdown
3. **Allows tutors to request withdrawals** that require admin approval
4. **Processes withdrawal requests** through various payment methods

## Automatic Fee Deduction

### When Lessons Are Completed

When a tutor marks a lesson as completed:

1. **Automatic Processing**: The system automatically calculates and processes earnings
2. **Platform Fee**: 20% platform commission is deducted
3. **Payment Status**: Lesson payment status is updated to 'paid'
4. **Earnings Record**: A transaction record is created for the tutor

### Fee Structure

```
Lesson Price: $50.00
Platform Fee (20%): $10.00
Tutor Payout: $40.00
```

## Tutor Earnings Tracking

### Earnings Summary

Tutors can view their complete earnings breakdown:

- **Total Gross Earnings**: Total amount from all completed lessons
- **Total Platform Fees**: Total fees paid to platform
- **Total Net Earnings**: Amount available to tutor
- **Available Balance**: Amount available for withdrawal
- **Pending Balance**: Earnings from recent lessons not yet processed
- **Total Hours Taught**: Total teaching time
- **Average Hourly Rate**: Calculated average rate

### API Endpoints

```javascript
// Get earnings summary
GET /api/earnings

// Get detailed earnings breakdown
GET /api/earnings/breakdown?startDate=2024-01-01&endDate=2024-12-31&limit=50

// Get available balance
GET /api/earnings/balance
```

## Withdrawal Request System

### Creating Withdrawal Requests

Tutors can request withdrawals through multiple methods:

#### Supported Withdrawal Methods

1. **Bank Transfer**
   - Account number, routing number, account holder name, bank name
   - Processing fee: $5.00 flat fee
   - Processing time: 3-5 business days

2. **PayPal**
   - PayPal email address
   - Processing fee: 2% of amount
   - Processing time: 1-3 business days

3. **Stripe**
   - Connected Stripe account ID
   - Processing fee: 2.5% of amount
   - Processing time: 1-2 business days

4. **Mobile Money**
   - Phone number and provider (MTN, Airtel, etc.)
   - Processing fee: 1.5% of amount
   - Processing time: 1-24 hours

### Withdrawal Requirements

- **Minimum Amount**: $10.00
- **Available Balance**: Must have sufficient balance
- **One Request**: Only one pending request allowed at a time
- **Admin Approval**: All requests require admin approval

### API Endpoints

```javascript
// Create withdrawal request
POST /api/earnings/withdraw
{
  "amount": 5000, // $50.00 in cents
  "withdrawalMethod": "bank_transfer",
  "destinationDetails": {
    "accountNumber": "**********",
    "routingNumber": "*********",
    "accountHolderName": "John Doe",
    "bankName": "Chase Bank"
  },
  "tutorNote": "Monthly withdrawal"
}

// Get withdrawal history
GET /api/earnings/withdrawals?limit=20

// Cancel pending request
DELETE /api/earnings/withdrawals/:requestId
```

## Admin Withdrawal Management

### Admin Dashboard

Admins can manage withdrawal requests:

```javascript
// Get pending requests
GET /api/earnings/admin/withdrawals/pending?limit=50

// Approve request
POST /api/earnings/admin/withdrawals/:requestId/approve
{
  "adminNotes": "Approved - verified bank details"
}

// Reject request
POST /api/earnings/admin/withdrawals/:requestId/reject
{
  "rejectionReason": "Invalid bank account details",
  "adminNotes": "Please provide correct routing number"
}
```

### Request Processing Flow

1. **Tutor Submits**: Withdrawal request created with status 'pending'
2. **Admin Reviews**: Admin approves or rejects with reason
3. **Processing**: Approved requests move to 'processing' status
4. **Completion**: External payment processed, status becomes 'completed'

## Example Usage

### Tutor Workflow

```javascript
// 1. Check available balance
const balance = await fetch('/api/earnings/balance');
console.log(balance.availableBalance); // "$125.50"

// 2. Create withdrawal request
const withdrawal = await fetch('/api/earnings/withdraw', {
  method: 'POST',
  body: JSON.stringify({
    amount: 10000, // $100.00
    withdrawalMethod: 'paypal',
    destinationDetails: {
      paypalEmail: '<EMAIL>'
    },
    tutorNote: 'Weekly withdrawal'
  })
});

// 3. Check request status
const requests = await fetch('/api/earnings/withdrawals');
console.log(requests.data[0].status); // "pending"
```

### Admin Workflow

```javascript
// 1. Get pending requests
const pending = await fetch('/api/earnings/admin/withdrawals/pending');

// 2. Review and approve
await fetch(`/api/earnings/admin/withdrawals/${requestId}/approve`, {
  method: 'POST',
  body: JSON.stringify({
    adminNotes: 'Verified PayPal account'
  })
});
```

## Database Models

### WithdrawalRequest Schema

```javascript
{
  tutorId: ObjectId,
  requestedAmount: Number, // in cents
  withdrawalMethod: String, // 'bank_transfer' | 'paypal' | 'stripe' | 'mobile_money'
  destinationDetails: Object,
  status: String, // 'pending' | 'approved' | 'rejected' | 'processing' | 'completed' | 'failed'
  processingFee: Number,
  finalAmount: Number,
  requestedAt: Date,
  reviewedBy: ObjectId, // Admin who reviewed
  reviewedAt: Date,
  adminNotes: String,
  rejectionReason: String
}
```

### Lesson Earnings Fields

```javascript
{
  // Existing lesson fields...
  tutorEarningId: ObjectId,
  platformFee: Number,
  tutorPayout: Number,
  paymentDeductedAt: Date,
  earningsReleasedAt: Date
}
```

## Security Features

- **Authentication**: All endpoints require valid JWT tokens
- **Authorization**: Role-based access (tutor/admin)
- **Validation**: Input validation for amounts and payment details
- **Audit Trail**: Complete history of all withdrawal requests
- **Balance Verification**: Prevents over-withdrawal

## Error Handling

Common error scenarios:

- **Insufficient Balance**: "Insufficient balance. Available: $X.XX"
- **Pending Request**: "You already have a pending withdrawal request"
- **Invalid Amount**: "Minimum withdrawal amount is $10.00"
- **Invalid Details**: "Bank transfer requires account number, account holder name, and bank name"

This system ensures secure, transparent, and efficient management of tutor earnings and withdrawals with full admin oversight.
