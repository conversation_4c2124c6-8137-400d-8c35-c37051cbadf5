# Automatic Calendar Creation

## Overview
This document describes the automatic calendar creation system that ensures every learner and tutor has a default calendar when they create an account.

## Features

### Automatic Calendar Creation
- **Registration**: Calendars are automatically created during user registration
- **User ID as Calendar ID**: Each user's calendar uses their user ID as the calendar ID for easy identification
- **Role-based Configuration**: Different default settings for students vs tutors
- **Timezone Support**: Calendars inherit the user's timezone preference

### Calendar Configuration by User Type

#### Student Calendars
- **Name**: "{Student Name}'s Learning Calendar"
- **Color**: Blue (#3B82F6)
- **Shared**: False (private by default)
- **Schedule**: No default schedule
- **Description**: "Default calendar for managing learning schedule and lessons"

#### Tutor Calendars
- **Name**: "{Tutor Name}'s Teaching Calendar"
- **Color**: Green (#10B981)
- **Shared**: True (visible to students by default)
- **Schedule**: Enabled for availability management
- **Description**: "Default calendar for managing teaching schedule and lessons"

## Implementation Details

### Calendar Creation Points

1. **User Registration** (`src/controllers/auth.ts`)
   - Triggered in `registerHook` function
   - Creates calendar immediately after user creation
   - Non-blocking (registration succeeds even if calendar creation fails)

2. **Direct User Creation** 
   - Tutor creation (`src/controllers/tutor.ts`)
   - Student creation (`src/controllers/student.ts`)
   - Calendar created after successful user creation

3. **Onboarding Completion** (`src/hooks/profile.ts`)
   - Ensures calendar exists when user completes onboarding
   - Fallback mechanism for users who might have missed calendar creation

4. **Middleware Enforcement** (`src/middlewares/ensureCalendar.ts`)
   - `ensureUserCalendar`: Creates calendar if missing (non-blocking)
   - `requireUserCalendar`: Creates calendar if missing (blocks request on failure)
   - `ensureBothUsersHaveCalendars`: For lesson scheduling

### Utility Functions (`src/utils/calendarUtils.ts`)

#### `createDefaultCalendar(options)`
Creates a default calendar for a user with the following options:
```typescript
{
  userId: Types.ObjectId;
  userType: 'student' | 'tutor';
  userName?: string;
  timezone?: string;
  customCalendarId?: string; // Optional custom calendar ID
}
```

#### `ensureUserHasCalendar(userId, userType, userName?, timezone?)`
Checks if user has a calendar and creates one if missing.

#### `getUserDefaultCalendar(userId, userType)`
Retrieves the user's default calendar.

#### `validateCalendarOwnership(calendarId, userId, userType)`
Validates that a calendar belongs to a specific user.

## Migration for Existing Users

### Migration Script
Run the migration script to create calendars for existing users:

```bash
# From the project root
npx ts-node src/scripts/createCalendarsForExistingUsers.ts
```

### Migration Features
- **Safe Execution**: Only creates calendars for users who don't have them
- **Progress Tracking**: Shows detailed progress and results
- **Error Handling**: Continues processing even if individual calendar creation fails
- **Validation**: Verifies migration success after completion

### Migration Output Example
```
🚀 Starting calendar migration for existing users...

Finding users without calendars...
Found 150 total users (75 students, 75 tutors)
Found 23 users without calendars

Creating calendars for 23 users...
✅ Created calendar for student John Doe (507f1f77bcf86cd799439011)
✅ Created calendar for tutor Jane Smith (507f1f77bcf86cd799439012)
...

📊 Migration Results:
✅ Successfully created: 23 calendars
❌ Failed to create: 0 calendars
📈 Success rate: 100.0%

🔍 Validating calendar creation...
👥 Total users: 150 (75 students, 75 tutors)
📅 Total calendars: 150 (75 student calendars, 75 tutor calendars)
✅ All users have calendars!

🎉 Calendar migration completed!
```

## API Integration

### Middleware Usage

#### Lesson Scheduling
```typescript
// Ensures both student and tutor have calendars before lesson scheduling
router.post('/schedule', 
  ...isAuthenticated(), 
  ensureBothUsersHaveCalendars, 
  validateSubscription, 
  scheduleLesson
);
```

#### Calendar Access
```typescript
// Ensures user has calendar when accessing calendar features
router.get('/my', 
  isAuthenticated(), 
  ensureUserCalendar, 
  getMyCalendars
);
```

### Dashboard Integration
The dashboard automatically shows calendar information and ensures users have calendars when accessing calendar-related features.

## Error Handling

### Non-blocking Calendar Creation
- User registration/creation never fails due to calendar creation issues
- Errors are logged but don't prevent user account creation
- Middleware provides fallback calendar creation

### Logging
All calendar creation attempts are logged with:
- User ID and type
- Success/failure status
- Error details (if any)
- Timestamp

### Fallback Mechanisms
1. **Registration**: If calendar creation fails during registration, middleware will retry
2. **Onboarding**: Calendar creation attempted again during onboarding completion
3. **Access**: Calendar created on-demand when user accesses calendar features

## Database Schema

### Calendar Document Structure
```typescript
{
  _id: ObjectId, // Same as user ID for default calendars
  ownerId: ObjectId, // User ID
  ownerType: 'student' | 'tutor',
  name: string,
  description: string,
  color: string,
  isShared: boolean,
  timezone: string,
  isActive: boolean,
  hasSchedule: boolean,
  scheduleSettings: {
    autoGenerateFromSchedule: boolean,
    generateDaysAhead: number,
    allowScheduleOverrides: boolean
  },
  displaySettings: {
    showSchedulePattern: boolean,
    showAvailabilityOnly: boolean,
    groupSimilarSlots: boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Indexes
- `{ ownerId: 1, ownerType: 1 }` - Primary lookup
- `{ ownerId: 1, ownerType: 1, isActive: 1 }` - Active calendars
- `{ isShared: 1, isActive: 1 }` - Shared calendars
- `{ ownerType: 1, isActive: 1 }` - Type-based queries

## Benefits

1. **Seamless User Experience**: Users automatically have calendars without manual setup
2. **Consistent Data**: All users have the same calendar structure
3. **Easy Integration**: Calendar ID matches user ID for simple lookups
4. **Role-based Defaults**: Appropriate settings for students vs tutors
5. **Backward Compatibility**: Migration script handles existing users
6. **Robust Error Handling**: Multiple fallback mechanisms ensure calendar creation

## Monitoring

### Health Checks
- Monitor calendar creation success rates
- Track users without calendars
- Alert on calendar creation failures

### Metrics to Track
- Calendar creation success rate during registration
- Number of users without calendars
- Calendar usage patterns by user type
- Migration completion status

## Troubleshooting

### Common Issues

1. **User has no calendar**: Run `ensureUserHasCalendar()` utility
2. **Calendar creation fails**: Check database permissions and connection
3. **Migration incomplete**: Re-run migration script (it's safe to run multiple times)
4. **Calendar ID conflicts**: Ensure user IDs are unique

### Debug Commands
```typescript
// Check if user has calendar
const calendar = await getUserDefaultCalendar(userId, userType);

// Force create calendar
const calendar = await createDefaultCalendar({
  userId,
  userType,
  userName,
  timezone
});

// Validate ownership
const isOwner = await validateCalendarOwnership(calendarId, userId, userType);
```
