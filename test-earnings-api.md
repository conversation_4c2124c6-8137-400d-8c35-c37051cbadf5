# Testing the Earnings and Withdrawal System

## API Endpoints to Test

### 1. Test Tutor Earnings Summary
```bash
curl -X GET http://localhost:8000/api/earnings \
  -H "Authorization: Bearer YOUR_TUTOR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalGrossEarnings": 0,
      "totalPlatformFees": 0,
      "totalNetEarnings": 0,
      "totalWithdrawn": 0,
      "availableBalance": 0,
      "pendingBalance": 0,
      "totalLessonsCompleted": 0,
      "totalHoursTaught": 0,
      "averageHourlyRate": 0
    },
    "recentEarnings": [],
    "formattedSummary": {
      "totalGrossEarnings": "$0.00",
      "totalPlatformFees": "$0.00",
      "totalNetEarnings": "$0.00",
      "availableBalance": "$0.00",
      "pendingBalance": "$0.00",
      "averageHourlyRate": "$0/hour"
    }
  }
}
```

### 2. Test Available Balance
```bash
curl -X GET http://localhost:8000/api/earnings/balance \
  -H "Authorization: Bearer YOUR_TUTOR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "totalEarnings": "$0.00",
    "totalWithdrawn": "$0.00",
    "availableBalance": "$0.00",
    "availableBalanceCents": 0,
    "minimumWithdrawal": "$10.00",
    "hasPendingRequest": false,
    "pendingRequestAmount": null
  }
}
```

### 3. Test Withdrawal Request Creation
```bash
curl -X POST http://localhost:8000/api/earnings/withdraw \
  -H "Authorization: Bearer YOUR_TUTOR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1000,
    "withdrawalMethod": "paypal",
    "destinationDetails": {
      "paypalEmail": "<EMAIL>"
    },
    "tutorNote": "Test withdrawal"
  }'
```

Expected Response (if insufficient balance):
```json
{
  "success": false,
  "message": "Insufficient balance. Available: $0.00"
}
```

### 4. Test Schedule Types
```bash
curl -X POST http://localhost:8000/api/schedules \
  -H "Authorization: Bearer YOUR_TUTOR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "calendarId": "YOUR_CALENDAR_ID",
    "name": "My Weekly Schedule",
    "description": "Regular weekly teaching schedule",
    "scheduleType": "weekly",
    "weeklySchedule": [
      {
        "dayOfWeek": 1,
        "isWorkingDay": true,
        "timeSlots": [
          {
            "startTime": "09:00",
            "endTime": "10:00",
            "isAvailable": true,
            "sessionType": "lesson",
            "price": 5000
          }
        ],
        "breakTimes": [],
        "dayNotes": ""
      }
    ],
    "isDefault": true
  }'
```

### 5. Test Admin Withdrawal Management
```bash
# Get pending withdrawals (Admin only)
curl -X GET http://localhost:8000/api/earnings/admin/withdrawals/pending \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json"

# Approve withdrawal (Admin only)
curl -X POST http://localhost:8000/api/earnings/admin/withdrawals/WITHDRAWAL_ID/approve \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "adminNotes": "Approved - verified payment details"
  }'
```

## Testing Workflow

1. **Create a tutor account** and get JWT token
2. **Create a calendar** for the tutor
3. **Create a schedule** with different schedule types
4. **Complete some lessons** to generate earnings
5. **Check earnings summary** to see accumulated earnings
6. **Create withdrawal request** when balance is sufficient
7. **Admin approves/rejects** withdrawal requests

## Schedule Types to Test

### Weekly Schedule
```json
{
  "scheduleType": "weekly",
  "weeklySchedule": [...]
}
```

### Daily Schedule
```json
{
  "scheduleType": "daily",
  "dailySchedule": {
    "dayOfWeek": 0,
    "isWorkingDay": true,
    "timeSlots": [...]
  }
}
```

### Flexible Schedule
```json
{
  "scheduleType": "flexible",
  "flexibleSchedule": {
    "availableHoursPerWeek": 20,
    "preferredTimeSlots": [...],
    "minimumNoticeHours": 24
  }
}
```

## Expected Behavior

1. **Automatic Earnings Processing**: When lessons are completed, earnings are automatically calculated and recorded
2. **Platform Fee Deduction**: 20% platform fee is automatically deducted
3. **Balance Tracking**: Available balance is updated in real-time
4. **Withdrawal Validation**: System prevents over-withdrawal and validates minimum amounts
5. **Admin Approval**: All withdrawal requests require admin approval
6. **Schedule Flexibility**: Multiple schedule types work correctly

## Error Cases to Test

1. **Insufficient Balance**: Try to withdraw more than available
2. **Invalid Payment Details**: Submit withdrawal with missing payment info
3. **Unauthorized Access**: Try to access admin endpoints as tutor
4. **Invalid Schedule Data**: Create schedule with missing required fields
5. **Duplicate Withdrawal**: Try to create multiple pending withdrawals

The system should handle all these cases gracefully with appropriate error messages.
